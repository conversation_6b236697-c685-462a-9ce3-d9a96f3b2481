// Status and Type Constants
// This file contains all status mappings and type definitions used across the application

// Vehicle Types
export const VEHICLE_TYPES = [
  'Truk Kecil',
  'Truk Sedang', 
  'Truk Besar',
  'Van',
  'Pick Up',
  'Mobil Box',
  'Container'
] as const;

export type VehicleType = typeof VEHICLE_TYPES[number];

// Vehicle Sources
export const VEHICLE_SOURCES = ['internal', 'vendor'] as const;
export type VehicleSource = typeof VEHICLE_SOURCES[number];

// Vehicle Status
export const VEHICLE_STATUSES = ['available', 'in-transit', 'maintenance', 'out-of-service'] as const;
export type VehicleStatus = typeof VEHICLE_STATUSES[number];

// Driver Types
export const DRIVER_TYPES = ['internal', 'vendor'] as const;
export type DriverType = typeof DRIVER_TYPES[number];

// Driver Status
export const DRIVER_STATUSES = ['active', 'inactive', 'suspended'] as const;
export type DriverStatus = typeof DRIVER_STATUSES[number];

// Order Status
export const ORDER_STATUSES = ['pending', 'assigned', 'in-progress', 'completed', 'invoiced'] as const;
export type OrderStatus = typeof ORDER_STATUSES[number];

// Order Priority
export const ORDER_PRIORITIES = ['low', 'normal', 'high', 'urgent'] as const;
export type OrderPriority = typeof ORDER_PRIORITIES[number];

// Invoice Status
export const INVOICE_STATUSES = ['draft', 'sent', 'paid', 'overdue'] as const;
export type InvoiceStatus = typeof INVOICE_STATUSES[number];

// Delivery Report Status
export const DELIVERY_REPORT_STATUSES = [
  'draft',
  'loading-reported',
  'unloading-reported', 
  'document-delivered',
  'completed'
] as const;
export type DeliveryReportStatus = typeof DELIVERY_REPORT_STATUSES[number];

// User Roles
export const USER_ROLES = ['admin', 'financial', 'operational', 'driver'] as const;
export type UserRole = typeof USER_ROLES[number];

// Status Text Mappings
export const STATUS_TEXT_MAPPINGS = {
  // Vehicle Status
  vehicle: {
    'available': 'Tersedia',
    'in-transit': 'Dalam Perjalanan',
    'maintenance': 'Maintenance',
    'out-of-service': 'Tidak Beroperasi'
  },
  
  // Driver Status
  driver: {
    'active': 'Aktif',
    'inactive': 'Tidak Aktif',
    'suspended': 'Suspended'
  },
  
  // Order Status
  order: {
    'pending': 'Menunggu',
    'assigned': 'Ditugaskan',
    'in-progress': 'Dalam Proses',
    'completed': 'Selesai',
    'invoiced': 'Ditagih'
  },
  
  // Order Priority
  priority: {
    'low': 'Rendah',
    'normal': 'Normal',
    'high': 'Tinggi',
    'urgent': 'Mendesak'
  },
  
  // Invoice Status
  invoice: {
    'draft': 'Draft',
    'sent': 'Terkirim',
    'paid': 'Lunas',
    'overdue': 'Terlambat'
  },
  
  // Driver Type
  driverType: {
    'internal': 'Internal',
    'vendor': 'Vendor'
  },
  
  // Vehicle Source
  vehicleSource: {
    'internal': 'Internal',
    'vendor': 'Vendor'
  }
} as const;

// Color Mappings for Status
export const STATUS_COLOR_MAPPINGS = {
  // Vehicle Status Colors
  vehicle: {
    'available': 'bg-green-500',
    'in-transit': 'bg-blue-500',
    'maintenance': 'bg-yellow-500',
    'out-of-service': 'bg-red-500'
  },
  
  // Order Status Colors
  order: {
    'pending': 'bg-yellow-100 text-yellow-800',
    'assigned': 'bg-blue-100 text-blue-800',
    'in-progress': 'bg-purple-100 text-purple-800',
    'completed': 'bg-green-100 text-green-800',
    'invoiced': 'bg-gray-100 text-gray-800'
  },
  
  // Order Priority Colors
  priority: {
    'low': 'bg-gray-100 text-gray-800',
    'normal': 'bg-blue-100 text-blue-800',
    'high': 'bg-orange-100 text-orange-800',
    'urgent': 'bg-red-100 text-red-800'
  },
  
  // Invoice Status Colors
  invoice: {
    'draft': 'bg-gray-500',
    'sent': 'bg-blue-500',
    'paid': 'bg-green-500',
    'overdue': 'bg-red-500'
  },
  
  // Driver Status Colors
  driver: {
    'active': 'bg-green-100 text-green-800',
    'inactive': 'bg-gray-100 text-gray-800',
    'suspended': 'bg-red-100 text-red-800'
  },
  
  // Vehicle Type Colors
  vehicleType: {
    'Truk Kecil': 'bg-amber-100 text-amber-800',
    'Truk Sedang': 'bg-orange-100 text-orange-800',
    'Truk Besar': 'bg-red-100 text-red-800',
    'Van': 'bg-green-100 text-green-800',
    'Pick Up': 'bg-blue-100 text-blue-800',
    'Mobil Box': 'bg-indigo-100 text-indigo-800',
    'Container': 'bg-purple-100 text-purple-800'
  },
  
  // Driver Type Colors
  driverType: {
    'internal': 'bg-blue-100 text-blue-800',
    'vendor': 'bg-purple-100 text-purple-800'
  },
  
  // Vehicle Source Colors
  vehicleSource: {
    'internal': 'bg-blue-100 text-blue-800',
    'vendor': 'bg-purple-100 text-purple-800'
  }
} as const;

// Default values
export const DEFAULT_VALUES = {
  vehicleStatus: 'available' as VehicleStatus,
  driverStatus: 'active' as DriverStatus,
  orderStatus: 'pending' as OrderStatus,
  orderPriority: 'normal' as OrderPriority,
  invoiceStatus: 'draft' as InvoiceStatus,
  driverType: 'internal' as DriverType,
  vehicleSource: 'internal' as VehicleSource
} as const;
