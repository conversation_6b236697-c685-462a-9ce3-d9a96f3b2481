import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Vehicle } from '@/types/vehicle';
import { Driver } from '@/types/driver';
import { Invoice } from '@/types/invoice';
import { DeliveryReport } from '@/types/deliveryReport';
import { STATUS_COLOR_MAPPINGS, STATUS_TEXT_MAPPINGS } from './constants';

// Vehicle Type Badges
export const getVehicleTypeBadge = (type: string) => {
  const colorClass = STATUS_COLOR_MAPPINGS.vehicleType[type as keyof typeof STATUS_COLOR_MAPPINGS.vehicleType];
  if (colorClass) {
    return <Badge className={colorClass}>{type}</Badge>;
  }
  return <Badge variant="outline">{type}</Badge>;
};

// Vehicle Source Badges
export const getVehicleSourceBadge = (source: string) => {
  const colorClass = STATUS_COLOR_MAPPINGS.vehicleSource[source as keyof typeof STATUS_COLOR_MAPPINGS.vehicleSource];
  const text = STATUS_TEXT_MAPPINGS.vehicleSource[source as keyof typeof STATUS_TEXT_MAPPINGS.vehicleSource];

  if (colorClass && text) {
    return <Badge className={colorClass}>{text}</Badge>;
  }
  return <Badge variant="outline">Unknown</Badge>;
};

// Vehicle Status Functions
export const getVehicleStatusColor = (status: string) => {
  return STATUS_COLOR_MAPPINGS.vehicle[status as keyof typeof STATUS_COLOR_MAPPINGS.vehicle] || 'bg-gray-500';
};

export const getVehicleStatusText = (status: string) => {
  return STATUS_TEXT_MAPPINGS.vehicle[status as keyof typeof STATUS_TEXT_MAPPINGS.vehicle] || status;
};

// Driver Status Badges
export const getDriverStatusBadge = (status: Driver['status']) => {
  const text = STATUS_TEXT_MAPPINGS.driver[status];
  const colorClass = STATUS_COLOR_MAPPINGS.driver[status];

  if (text && colorClass) {
    return <Badge className={colorClass}>{text}</Badge>;
  }

  // Fallback for specific cases
  if (status === 'inactive') {
    return <Badge variant="secondary">Tidak Aktif</Badge>;
  }
  if (status === 'suspended') {
    return <Badge variant="destructive">Suspended</Badge>;
  }

  return <Badge variant="outline">Unknown</Badge>;
};

// Driver Type Badges
export const getDriverTypeBadge = (driverType: Driver['driverType']) => {
  const text = STATUS_TEXT_MAPPINGS.driverType[driverType];
  const colorClass = STATUS_COLOR_MAPPINGS.driverType[driverType];

  if (text && colorClass) {
    return <Badge className={colorClass}>{text}</Badge>;
  }
  return <Badge variant="outline">Unknown</Badge>;
};

// Order Status Functions
export const getOrderStatusColor = (status: string) => {
  return STATUS_COLOR_MAPPINGS.order[status as keyof typeof STATUS_COLOR_MAPPINGS.order] || 'bg-gray-100 text-gray-800';
};

export const getOrderStatusText = (status: string) => {
  return STATUS_TEXT_MAPPINGS.order[status as keyof typeof STATUS_TEXT_MAPPINGS.order] || status;
};

// Order Priority Functions
export const getOrderPriorityColor = (priority: string) => {
  return STATUS_COLOR_MAPPINGS.priority[priority as keyof typeof STATUS_COLOR_MAPPINGS.priority] || 'bg-gray-100 text-gray-800';
};

export const getOrderPriorityText = (priority: string) => {
  return STATUS_TEXT_MAPPINGS.priority[priority as keyof typeof STATUS_TEXT_MAPPINGS.priority] || priority;
};

// Invoice Status Functions
export const getInvoiceStatusColor = (status: Invoice['status']) => {
  return STATUS_COLOR_MAPPINGS.invoice[status] || 'bg-gray-500';
};

export const getInvoiceStatusText = (status: Invoice['status']) => {
  return STATUS_TEXT_MAPPINGS.invoice[status] || status;
};

// Delivery Report Status Badges
export const getDeliveryReportStatusBadge = (status: DeliveryReport['status']) => {
  switch (status) {
    case 'draft':
      return <Badge variant="secondary">Draft</Badge>;
    case 'loading-reported':
      return <Badge className="bg-yellow-500">Tahap Muat Selesai</Badge>;
    case 'unloading-reported':
      return <Badge className="bg-blue-500">Tahap Bongkar Selesai</Badge>;
    case 'document-delivered':
      return <Badge className="bg-purple-500">Dokumen Terkirim</Badge>;
    case 'completed':
      return <Badge className="bg-green-500">Selesai</Badge>;
    default:
      return <Badge variant="outline">Unknown</Badge>;
  }
};
