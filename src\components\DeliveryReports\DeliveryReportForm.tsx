
import React from 'react';
import { DeliveryReportFormData } from '@/types/deliveryReport';
import InternalDriverForm from './InternalDriverForm';
import VendorLoadingForm from './VendorLoadingForm';
import VendorUnloadingForm from './VendorUnloadingForm';
import VendorDocumentDeliveryForm from './VendorDocumentDeliveryForm';

interface DeliveryReportFormProps {
  orderId: string;
  orderNumber: string;
  driverType: 'internal' | 'vendor';
  stage?: 'loading' | 'unloading' | 'document-delivery';
  onSubmit: (data: DeliveryReportFormData) => void;
  onCancel: () => void;
}

const DeliveryReportForm = ({ 
  orderId, 
  orderNumber, 
  driverType, 
  stage = 'loading', 
  onSubmit, 
  onCancel 
}: DeliveryReportFormProps) => {
  
  if (driverType === 'internal') {
    return (
      <InternalDriverForm
        orderId={orderId}
        orderNumber={orderNumber}
        onSubmit={onSubmit}
        onCancel={onCancel}
      />
    );
  }

  // Vendor driver forms based on stage
  switch (stage) {
    case 'loading':
      return (
        <VendorLoadingForm
          orderId={orderId}
          orderNumber={orderNumber}
          onSubmit={onSubmit}
          onCancel={onCancel}
        />
      );
    case 'unloading':
      return (
        <VendorUnloadingForm
          orderId={orderId}
          orderNumber={orderNumber}
          onSubmit={onSubmit}
          onCancel={onCancel}
        />
      );
    case 'document-delivery':
      return (
        <VendorDocumentDeliveryForm
          orderId={orderId}
          orderNumber={orderNumber}
          onSubmit={onSubmit}
          onCancel={onCancel}
        />
      );
    default:
      return (
        <VendorLoadingForm
          orderId={orderId}
          orderNumber={orderNumber}
          onSubmit={onSubmit}
          onCancel={onCancel}
        />
      );
  }
};

export default DeliveryReportForm;
