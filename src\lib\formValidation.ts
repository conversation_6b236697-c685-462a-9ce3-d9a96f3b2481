import * as z from 'zod';

// Common validation patterns
export const ValidationPatterns = {
  // Indonesian phone number pattern
  phoneNumber: /^(\+62|62|0)[0-9]{9,13}$/,
  // Indonesian license plate pattern
  licensePlate: /^[A-Z]{1,2}\s?\d{1,4}\s?[A-Z]{1,3}$/i,
  // Email pattern (more permissive)
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  // License number pattern
  licenseNumber: /^[A-Z0-9]{5,20}$/i,
  // Postal code pattern (Indonesian)
  postalCode: /^\d{5}$/,
} as const;

// Common validation messages
export const ValidationMessages = {
  required: (field: string) => `${field} wajib diisi`,
  minLength: (field: string, min: number) => `${field} minimal ${min} karakter`,
  maxLength: (field: string, max: number) => `${field} maksimal ${max} karakter`,
  email: 'Format email tidak valid',
  phone: 'Format nomor telepon tidak valid (contoh: +62812-3456-7890)',
  licensePlate: 'Format nomor plat tidak valid (contoh: B 1234 ABC)',
  licenseNumber: 'Format nomor SIM tidak valid',
  postalCode: 'Kode pos harus 5 digit',
  positiveNumber: (field: string) => `${field} harus berupa angka positif`,
  dateInFuture: (field: string) => `${field} harus tanggal di masa depan`,
  dateInPast: (field: string) => `${field} harus tanggal di masa lalu`,
  minValue: (field: string, min: number) => `${field} minimal ${min}`,
  maxValue: (field: string, max: number) => `${field} maksimal ${max}`,
} as const;

// Reusable validation schemas
export const CommonValidations = {
  // Basic string validations
  requiredString: (fieldName: string, minLength = 1) =>
    z.string().min(minLength, ValidationMessages.minLength(fieldName, minLength)),

  optionalString: (maxLength?: number) =>
    maxLength 
      ? z.string().max(maxLength, ValidationMessages.maxLength('Field', maxLength)).optional()
      : z.string().optional(),

  // Name validation
  name: (fieldName = 'Nama') =>
    z.string()
      .min(2, ValidationMessages.minLength(fieldName, 2))
      .max(100, ValidationMessages.maxLength(fieldName, 100)),

  // Email validation
  email: z.string()
    .min(1, ValidationMessages.required('Email'))
    .regex(ValidationPatterns.email, ValidationMessages.email),

  // Phone validation
  phone: z.string()
    .min(1, ValidationMessages.required('Nomor telepon'))
    .regex(ValidationPatterns.phoneNumber, ValidationMessages.phone),

  // License plate validation
  licensePlate: z.string()
    .min(1, ValidationMessages.required('Nomor plat'))
    .regex(ValidationPatterns.licensePlate, ValidationMessages.licensePlate),

  // License number validation
  licenseNumber: z.string()
    .min(1, ValidationMessages.required('Nomor SIM'))
    .regex(ValidationPatterns.licenseNumber, ValidationMessages.licenseNumber),

  // Address validation
  address: (fieldName = 'Alamat') =>
    z.string()
      .min(10, ValidationMessages.minLength(fieldName, 10))
      .max(500, ValidationMessages.maxLength(fieldName, 500)),

  // Positive number validation
  positiveNumber: (fieldName: string) =>
    z.number()
      .positive(ValidationMessages.positiveNumber(fieldName)),

  // Positive integer validation
  positiveInteger: (fieldName: string) =>
    z.number()
      .int('Harus berupa bilangan bulat')
      .positive(ValidationMessages.positiveNumber(fieldName)),

  // Year validation
  year: z.number()
    .int('Tahun harus berupa bilangan bulat')
    .min(1900, ValidationMessages.minValue('Tahun', 1900))
    .max(new Date().getFullYear() + 1, ValidationMessages.maxValue('Tahun', new Date().getFullYear() + 1)),

  // Date validation
  futureDate: (fieldName: string) =>
    z.string()
      .min(1, ValidationMessages.required(fieldName))
      .refine((date) => new Date(date) > new Date(), ValidationMessages.dateInFuture(fieldName)),

  pastDate: (fieldName: string) =>
    z.string()
      .min(1, ValidationMessages.required(fieldName))
      .refine((date) => new Date(date) < new Date(), ValidationMessages.dateInPast(fieldName)),

  // Weight validation (in kg)
  weight: z.string()
    .min(1, ValidationMessages.required('Berat'))
    .refine((val) => !isNaN(Number(val)) && Number(val) > 0, 'Berat harus berupa angka positif'),

  // Capacity validation (in kg)
  capacity: z.number()
    .positive('Kapasitas harus berupa angka positif')
    .max(50000, 'Kapasitas maksimal 50,000 kg'),

  // Notes validation
  notes: z.string()
    .max(1000, ValidationMessages.maxLength('Catatan', 1000))
    .optional(),
} as const;

// Predefined schemas for common entities
export const EntitySchemas = {
  // Driver schema
  driver: z.object({
    name: CommonValidations.name('Nama'),
    email: CommonValidations.email,
    phone: CommonValidations.phone,
    licenseNumber: CommonValidations.licenseNumber,
    licenseExpiry: CommonValidations.futureDate('Tanggal kadaluarsa SIM'),
    address: CommonValidations.address('Alamat'),
    driverType: z.enum(['internal', 'vendor'], { 
      required_error: ValidationMessages.required('Jenis driver') 
    }),
    vehicleTypes: CommonValidations.requiredString('Jenis kendaraan'),
    emergencyContactName: CommonValidations.optionalString(100),
    emergencyContactPhone: z.string().regex(ValidationPatterns.phoneNumber, ValidationMessages.phone).optional().or(z.literal('')),
    emergencyContactRelation: CommonValidations.optionalString(50),
  }),

  // Vehicle schema
  vehicle: z.object({
    plateNumber: CommonValidations.licensePlate,
    type: z.enum(['Truk Kecil', 'Truk Sedang', 'Truk Besar', 'Van', 'Pick Up', 'Mobil Box', 'Container'], {
      required_error: ValidationMessages.required('Jenis kendaraan')
    }),
    vehicleSource: z.enum(['internal', 'vendor'], {
      required_error: ValidationMessages.required('Sumber kendaraan')
    }),
    brand: CommonValidations.requiredString('Merek', 2),
    model: CommonValidations.requiredString('Model', 2),
    year: CommonValidations.year,
    capacity: CommonValidations.capacity,
    fuelType: z.enum(['Bensin', 'Solar', 'Listrik', 'Hybrid'], {
      required_error: ValidationMessages.required('Jenis bahan bakar')
    }),
    registrationExpiry: CommonValidations.futureDate('Tanggal kadaluarsa STNK'),
    insuranceExpiry: CommonValidations.futureDate('Tanggal kadaluarsa asuransi'),
    notes: CommonValidations.notes,
  }),

  // Order schema
  order: z.object({
    customerName: CommonValidations.name('Nama pelanggan'),
    customerAddress: CommonValidations.address('Alamat pelanggan'),
    customerPhone: CommonValidations.phone,
    pickupAddress: CommonValidations.address('Alamat penjemputan'),
    deliveryAddress: CommonValidations.address('Alamat pengiriman'),
    pickupDate: z.date({ required_error: ValidationMessages.required('Tanggal penjemputan') }),
    deliveryDate: z.date({ required_error: ValidationMessages.required('Tanggal pengiriman') }),
    cargoDescription: CommonValidations.requiredString('Deskripsi barang', 5),
    weight: CommonValidations.weight,
    priority: z.enum(['low', 'normal', 'high', 'urgent'], {
      required_error: ValidationMessages.required('Prioritas')
    }),
    notes: CommonValidations.notes,
  }),
} as const;

// Form validation helpers
export const FormValidationHelpers = {
  // Validate Indonesian phone number
  validatePhoneNumber: (phone: string): boolean => {
    return ValidationPatterns.phoneNumber.test(phone);
  },

  // Validate license plate
  validateLicensePlate: (plate: string): boolean => {
    return ValidationPatterns.licensePlate.test(plate);
  },

  // Validate email
  validateEmail: (email: string): boolean => {
    return ValidationPatterns.email.test(email);
  },

  // Format phone number for display
  formatPhoneNumber: (phone: string): string => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.startsWith('62')) {
      return `+${cleaned}`;
    }
    if (cleaned.startsWith('0')) {
      return `+62${cleaned.slice(1)}`;
    }
    return phone;
  },

  // Format license plate for display
  formatLicensePlate: (plate: string): string => {
    return plate.toUpperCase().replace(/\s+/g, ' ').trim();
  },

  // Check if date is in the future
  isDateInFuture: (date: string | Date): boolean => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj > new Date();
  },

  // Check if date is expired (in the past)
  isDateExpired: (date: string | Date): boolean => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    dateObj.setHours(0, 0, 0, 0);
    return dateObj < today;
  },

  // Get validation error message for a field
  getFieldError: (errors: any, fieldName: string): string | undefined => {
    return errors[fieldName]?.message;
  },

  // Check if form has any errors
  hasFormErrors: (errors: any): boolean => {
    return Object.keys(errors).length > 0;
  },
} as const;

// Export types for TypeScript
export type DriverFormData = z.infer<typeof EntitySchemas.driver>;
export type VehicleFormData = z.infer<typeof EntitySchemas.vehicle>;
export type OrderFormData = z.infer<typeof EntitySchemas.order>;
