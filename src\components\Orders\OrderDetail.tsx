
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import { ArrowLeft, Calendar, Truck, Package, MapPin } from 'lucide-react';

interface OrderDetailProps {
  order: any;
  onBack: () => void;
}

const OrderDetail = ({ order, onBack }: OrderDetailProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'assigned': return 'bg-blue-100 text-blue-800';
      case 'in-progress': return 'bg-purple-100 text-purple-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'invoiced': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-gray-100 text-gray-800';
      case 'normal': return 'bg-blue-100 text-blue-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'urgent': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Menunggu';
      case 'assigned': return 'Ditugaskan';
      case 'in-progress': return 'Dalam Proses';
      case 'completed': return 'Selesai';
      case 'invoiced': return 'Ditagih';
      default: return status;
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'low': return 'Rendah';
      case 'normal': return 'Normal';
      case 'high': return 'Tinggi';
      case 'urgent': return 'Mendesak';
      default: return priority;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <ScrollArea className="h-[calc(100vh-120px)] w-full">
        <div className="space-y-4 sm:space-y-6 pr-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <Button
              variant="outline"
              onClick={onBack}
              className="flex items-center gap-2 w-full sm:w-auto"
            >
              <ArrowLeft className="h-4 w-4" />
              Kembali ke Daftar
            </Button>
            <div className="space-y-1">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Detail Pesanan</h1>
              <p className="text-sm sm:text-base text-gray-600">Informasi lengkap pesanan transportasi</p>
            </div>
          </div>

          <Card className="w-full">
            <CardHeader className="space-y-3">
              <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
                <div className="space-y-1 min-w-0 flex-1">
                  <CardTitle className="text-xl sm:text-2xl break-words">{order.orderNumber}</CardTitle>
                  <p className="text-base sm:text-lg text-gray-600 break-words">{order.customerName}</p>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Badge className={getPriorityColor(order.priority)}>
                    {getPriorityText(order.priority)}
                  </Badge>
                  <Badge className={getStatusColor(order.status)}>
                    {getStatusText(order.status)}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <MapPin className="h-5 w-5 text-green-600 mt-1 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <p className="font-semibold text-gray-900">Alamat Penjemputan</p>
                      <p className="text-gray-600 break-words">{order.pickupAddress}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Calendar className="h-5 w-5 text-blue-600 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <p className="font-semibold text-gray-900">Tanggal Jemput</p>
                      <p className="text-gray-600">
                        {format(order.pickupDate, "EEEE, dd MMMM yyyy", { locale: id })}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <MapPin className="h-5 w-5 text-red-600 mt-1 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <p className="font-semibold text-gray-900">Alamat Pengiriman</p>
                      <p className="text-gray-600 break-words">{order.deliveryAddress}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Calendar className="h-5 w-5 text-purple-600 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <p className="font-semibold text-gray-900">Tanggal Kirim</p>
                      <p className="text-gray-600">
                        {format(order.deliveryDate, "EEEE, dd MMMM yyyy", { locale: id })}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {order.assignedDriver && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Truck className="h-5 w-5 text-blue-600 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <p className="font-semibold text-gray-900">Driver yang Ditugaskan</p>
                      <p className="text-gray-600 break-words">{order.assignedDriver}</p>
                    </div>
                  </div>
                </div>
              )}

              {order.cargoDescription && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Package className="h-5 w-5 text-gray-600 flex-shrink-0" />
                    <p className="font-semibold text-gray-900">Deskripsi Kargo</p>
                  </div>
                  <p className="text-gray-600 bg-gray-50 p-3 rounded-lg break-words">{order.cargoDescription}</p>
                </div>
              )}

              {order.amount > 0 && (
                <div className="bg-green-50 p-4 rounded-lg">
                  <p className="text-sm text-gray-600">Total Biaya</p>
                  <p className="text-xl sm:text-2xl font-bold text-green-600">
                    {formatCurrency(order.amount)}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </ScrollArea>
    </div>
  );
};

export default OrderDetail;
