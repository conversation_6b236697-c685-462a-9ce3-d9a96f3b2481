import React from 'react';
import DeliveryReportList from './DeliveryReportList';
import DriverReportSection from './DriverReportSection';
import { DeliveryReport, DeliveryReportFormData } from '@/types/deliveryReport';
import { Order } from '@/types/order';
import { User } from '@/types/user';

interface DeliveryReportSectionProps {
  showDeliveryReportForm: boolean;
  selectedDeliveryReport: DeliveryReport | null;
  deliveryReportStage?: 'loading' | 'unloading' | 'document-delivery';
  user: User;
  deliveryReports: DeliveryReport[];
  orders: Order[];
  onCreateDeliveryReport: (orderId: string) => void;
  onContinueDeliveryReport: (report: DeliveryReport, stage: 'unloading' | 'document-delivery') => void;
  onSubmitDeliveryReport: (reportData: DeliveryReportFormData) => void;
  onCancelDeliveryReportForm: () => void;
  onBackToDeliveryReportList: () => void;
}

const DeliveryReportSection = ({
  showDeliveryReportForm,
  selectedDeliveryReport,
  deliveryReportStage,
  user,
  deliveryReports,
  orders,
  onCreateDeliveryReport,
  onContinueDeliveryReport,
  onSubmitDeliveryReport,
  onCancelDeliveryReportForm,
  onBackToDeliveryReportList
}: DeliveryReportSectionProps) => {
  // If user is a driver, show the driver report section
  if (user.role === 'driver') {
    return (
      <DriverReportSection
        currentView={user.driverType === 'internal' ? 'loading-report' : 'unloading-report'}
        driverType={user.driverType || 'internal'}
        selectedOrder={orders.find(o => o.driverId === user.id && o.status === 'assigned')}
        selectedReport={selectedDeliveryReport}
        onCreateReport={onSubmitDeliveryReport}
        onUpdateReport={(reportId, data, stage) => {
          // Handle the update report logic
          onSubmitDeliveryReport({...data, reportId});
        }}
        onCancel={onCancelDeliveryReportForm}
        onBackToList={onBackToDeliveryReportList}
      />
    );
  }

  // For admin/operational users, show the delivery report list
  return (
    <div className="max-w-full">
      <div className="space-y-4 sm:space-y-6">
        <div className="space-y-1">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Laporan Pengiriman</h1>
          <p className="text-sm sm:text-base text-gray-600">Kelola laporan pengiriman dari driver</p>
        </div>
        
        <DeliveryReportList
          reports={deliveryReports}
          orders={orders}
          driverType={user.driverType || 'internal'}
          onCreateReport={onCreateDeliveryReport}
          onContinueReport={onContinueDeliveryReport}
        />
      </div>
    </div>
  );
};

export default DeliveryReportSection;