import React from 'react';
import DeliveryReportList from '@/components/DeliveryReports/DeliveryReportList';
import DeliveryReportForm from '@/components/DeliveryReports/DeliveryReportForm';
import { DeliveryReport, DeliveryReportFormData } from '@/types/deliveryReport';
import { Order } from '@/types/order';

interface DeliveryReportSectionProps {
  showDeliveryReportForm: boolean;
  selectedDeliveryReport: DeliveryReport | null;
  deliveryReportStage: 'loading' | 'unloading' | 'document-delivery';
  user: { name: string; role: 'admin' | 'financial' | 'operational' | 'driver'; driverType?: 'internal' | 'vendor' };
  deliveryReports: DeliveryReport[];
  orders: Order[];
  onCreateDeliveryReport: (orderId: string) => void;
  onContinueDeliveryReport: (report: DeliveryReport, stage: 'unloading' | 'document-delivery') => void;
  onSubmitDeliveryReport: (data: DeliveryReportFormData) => void;
  onCancelDeliveryReportForm: () => void;
  onBackToDeliveryReportList: () => void;
}

const DeliveryReportSection = ({
  showDeliveryReportForm,
  selectedDeliveryReport,
  deliveryReportStage,
  user,
  deliveryReports,
  orders,
  onCreateDeliveryReport,
  onContinueDeliveryReport,
  onSubmitDeliveryReport,
  onCancelDeliveryReportForm,
  onBackToDeliveryReportList,
}: DeliveryReportSectionProps) => {
  // Handle delivery report form display
  if (showDeliveryReportForm && selectedDeliveryReport) {
    return (
      <div className="max-w-full overflow-auto">
        <DeliveryReportForm
          orderId={selectedDeliveryReport.orderId}
          orderNumber={selectedDeliveryReport.orderNumber}
          driverType={user.driverType || 'internal'}
          stage={deliveryReportStage}
          onSubmit={onSubmitDeliveryReport}
          onCancel={onCancelDeliveryReportForm}
        />
      </div>
    );
  }

  // Default delivery report list view
  return (
    <div className="max-w-full">
      <DeliveryReportList
        reports={deliveryReports}
        orders={orders}
        driverType={user.driverType || 'internal'}
        onCreateReport={onCreateDeliveryReport}
        onContinueReport={onContinueDeliveryReport}
      />
    </div>
  );
};

export default DeliveryReportSection;
