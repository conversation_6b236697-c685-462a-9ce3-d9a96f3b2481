
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import PhotoUploadComponent from './PhotoUploadComponent';
import { DeliveryReportFormData } from '@/types/deliveryReport';
import { useToast } from '@/hooks/use-toast';

interface VendorUnloadingFormProps {
  orderId: string;
  orderNumber: string;
  onSubmit: (data: DeliveryReportFormData) => void;
  onCancel: () => void;
}

const VendorUnloadingForm = ({ orderId, orderNumber, onSubmit, onCancel }: VendorUnloadingFormProps) => {
  const [formData, setFormData] = useState<DeliveryReportFormData>({
    orderId,
    driverType: 'vendor',
    unloadingDate: new Date(),
    unloadingPhotos: [],
    unloadingVideo: undefined,
    waybillPhotosUnloading: []
  });

  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.unloadingPhotos || formData.unloadingPhotos.length === 0) {
      toast({
        title: "Form tidak lengkap", 
        description: "Mohon upload foto kegiatan bongkar",
        variant: "destructive"
      });
      return;
    }

    if (!formData.waybillPhotosUnloading || formData.waybillPhotosUnloading.length < 2) {
      toast({
        title: "Form tidak lengkap",
        description: "Mohon upload 2 foto surat jalan",
        variant: "destructive"
      });
      return;
    }

    onSubmit(formData);
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold">
            Lapor Pengiriman - Tahap 2: Bongkar Barang
          </CardTitle>
          <p className="text-sm text-gray-600">
            Pesanan: {orderNumber}
          </p>
          <div className="flex items-center space-x-2 text-sm">
            <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center">✓</div>
            <span className="text-green-600">Muat</span>
            <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center">2</div>
            <span className="font-medium">Bongkar</span>
            <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center">3</div>
            <span className="text-gray-500">Kirim Dokumen</span>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="unloadingDate">Tanggal Bongkar</Label>
              <Input
                id="unloadingDate"
                type="date"
                value={formData.unloadingDate?.toISOString().split('T')[0] || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  unloadingDate: new Date(e.target.value)
                }))}
                required
              />
            </div>

            <PhotoUploadComponent
              label="Foto/Video Kegiatan Bongkar"
              maxFiles={5}
              acceptVideo={true}
              files={[...(formData.unloadingPhotos || []), ...(formData.unloadingVideo ? [formData.unloadingVideo] : [])]}
              onFilesChange={(files) => {
                const videos = files.filter(f => f.type.startsWith('video/'));
                const photos = files.filter(f => f.type.startsWith('image/'));
                setFormData(prev => ({
                  ...prev,
                  unloadingPhotos: photos,
                  unloadingVideo: videos[0] || undefined
                }));
              }}
              required
            />

            <PhotoUploadComponent
              label="Foto Surat Jalan (2 foto)"
              maxFiles={2}
              acceptVideo={false}
              files={formData.waybillPhotosUnloading || []}
              onFilesChange={(files) => setFormData(prev => ({
                ...prev,
                waybillPhotosUnloading: files
              }))}
              required
            />

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button type="submit" className="flex-1">
                Laporkan Tahap Bongkar
              </Button>
              <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
                Batal
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default VendorUnloadingForm;
