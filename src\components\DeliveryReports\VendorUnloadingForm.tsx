
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DeliveryReportFormData } from '@/types/deliveryReport';
import { useToast } from '@/hooks/use-toast';
import { FileUploader } from '@/components/ui/file-uploader';

interface VendorUnloadingFormProps {
  reportId: string;
  orderNumber: string;
  onSubmit: (data: DeliveryReportFormData) => void;
  onCancel: () => void;
}

const VendorUnloadingForm = ({ reportId, orderNumber, onSubmit, onCancel }: VendorUnloadingFormProps) => {
  const [formData, setFormData] = useState<DeliveryReportFormData>({
    reportId,
    driverType: 'vendor',
    unloadingDate: new Date(),
    unloadingPhotos: [],
    waybillPhotosUnloading: []
  });

  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.unloadingDate || formData.unloadingPhotos?.length === 0 || formData.waybillPhotosUnloading?.length < 2) {
      toast({
        title: "Form tidak lengkap",
        description: "Mohon isi tanggal bongkar, unggah foto kegiatan bongkar, dan 2 foto surat jalan",
        variant: "destructive"
      });
      return;
    }

    onSubmit(formData);
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold">
            Lapor Bongkar - Driver Vendor
          </CardTitle>
          <p className="text-sm text-gray-600">
            Pesanan: {orderNumber}
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="unloadingDate">Tanggal Bongkar</Label>
              <Input
                id="unloadingDate"
                type="date"
                value={formData.unloadingDate?.toISOString().split('T')[0] || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  unloadingDate: new Date(e.target.value)
                }))}
                required
              />
            </div>

            <div className="space-y-2">
              <Label>Foto Surat Jalan (2 foto)</Label>
              <FileUploader
                accept="image/*"
                maxFiles={2}
                onFilesSelected={(files) => setFormData(prev => ({
                  ...prev,
                  waybillPhotosUnloading: files
                }))}
              />
              <p className="text-xs text-gray-500">Unggah 2 foto surat jalan</p>
            </div>

            <div className="space-y-2">
              <Label>Foto Kegiatan Bongkar</Label>
              <FileUploader
                accept="image/*"
                maxFiles={5}
                onFilesSelected={(files) => setFormData(prev => ({
                  ...prev,
                  unloadingPhotos: files
                }))}
              />
              <p className="text-xs text-gray-500">Unggah foto kegiatan bongkar (maks. 5 foto)</p>
            </div>

            <div className="space-y-2">
              <Label>Video Kegiatan Bongkar (Opsional)</Label>
              <FileUploader
                accept="video/*"
                maxFiles={1}
                onFilesSelected={(files) => setFormData(prev => ({
                  ...prev,
                  unloadingVideo: files[0]
                }))}
              />
              <p className="text-xs text-gray-500">Unggah video kegiatan bongkar (opsional)</p>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={onCancel}>
                Batal
              </Button>
              <Button type="submit">
                Kirim Laporan
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default VendorUnloadingForm;
