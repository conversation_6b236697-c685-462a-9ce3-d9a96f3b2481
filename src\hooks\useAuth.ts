
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';

// Mock user data for demo
const mockUsers = {
  '<EMAIL>': { name: 'Administrator', role: 'admin' as const },
  '<EMAIL>': { name: '<PERSON>uang<PERSON>', role: 'financial' as const },
  '<EMAIL>': { name: 'Staff Operasional', role: 'operational' as const },
  '<EMAIL>': { name: 'Driver', role: 'driver' as const }
};

export const useAuth = () => {
  const [user, setUser] = useState<{ name: string; role: 'admin' | 'financial' | 'operational' | 'driver' } | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleLogin = async (email: string, password: string) => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockUser = mockUsers[email as keyof typeof mockUsers];
    if (mockUser && password === 'password') {
      setUser(mockUser);
      toast({
        title: "Login berhasil",
        description: `Selamat datang, ${mockUser.name}!`,
      });
    } else {
      toast({
        title: "Login gagal",
        description: "Email atau password tidak valid",
        variant: "destructive",
      });
    }
    setIsLoading(false);
  };

  const handleLogout = () => {
    setUser(null);
    toast({
      title: "Logout berhasil",
      description: "Anda telah keluar dari sistem",
    });
  };

  return {
    user,
    isLoading,
    handleLogin,
    handleLogout
  };
};
