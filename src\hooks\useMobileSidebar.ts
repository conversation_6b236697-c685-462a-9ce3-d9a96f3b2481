
import { useState, useEffect, useCallback, useRef } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';

interface UseMobileSidebarReturn {
  isVisible: boolean;
  showSidebar: () => void;
  hideSidebar: () => void;
  hoverZoneProps: {
    onMouseEnter: () => void;
    onMouseLeave: () => void;
  };
  sidebarProps: {
    onMouseLeave: () => void;
  };
}

export const useMobileSidebar = (): UseMobileSidebarReturn => {
  const [isVisible, setIsVisible] = useState(false);
  const isMobile = useIsMobile();
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const showSidebar = useCallback(() => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = null;
    }
    setIsVisible(true);
  }, []);

  const hideSidebar = useCallback(() => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
    
    hideTimeoutRef.current = setTimeout(() => {
      setIsVisible(false);
    }, 200); // Small delay to prevent flickering
  }, []);

  const handleHoverZoneEnter = useCallback(() => {
    if (!isMobile) return;
    
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
    
    hoverTimeoutRef.current = setTimeout(() => {
      showSidebar();
    }, 2000); // 2 seconds delay
  }, [isMobile, showSidebar]);

  const handleHoverZoneLeave = useCallback(() => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
  }, []);

  const handleSidebarLeave = useCallback(() => {
    if (!isMobile) return;
    hideSidebar();
  }, [isMobile, hideSidebar]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, []);

  // On desktop, always show sidebar
  useEffect(() => {
    if (!isMobile) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, [isMobile]);

  return {
    isVisible,
    showSidebar,
    hideSidebar,
    hoverZoneProps: {
      onMouseEnter: handleHoverZoneEnter,
      onMouseLeave: handleHoverZoneLeave,
    },
    sidebarProps: {
      onMouseLeave: handleSidebarLeave,
    },
  };
};
