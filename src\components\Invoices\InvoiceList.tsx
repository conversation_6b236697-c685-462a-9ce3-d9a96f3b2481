
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, Edit, Printer } from 'lucide-react';
import { Invoice } from '@/types/invoice';

interface InvoiceListProps {
  invoices: Invoice[];
  onViewInvoice: (invoice: Invoice) => void;
  onEditInvoice: (invoice: Invoice) => void;
  onUpdateStatus: (invoiceId: string, status: Invoice['status']) => void;
}

const InvoiceList = ({ invoices, onViewInvoice, onEditInvoice, onUpdateStatus }: InvoiceListProps) => {
  const getStatusColor = (status: Invoice['status']) => {
    switch (status) {
      case 'draft': return 'bg-gray-500';
      case 'sent': return 'bg-blue-500';
      case 'paid': return 'bg-green-500';
      case 'overdue': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = (status: Invoice['status']) => {
    switch (status) {
      case 'draft': return 'Draft';
      case 'sent': return 'Terkirim';
      case 'paid': return 'Lunas';
      case 'overdue': return 'Terlambat';
      default: return status;
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {invoices.map((invoice) => (
          <Card key={invoice.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg font-bold">{invoice.invoiceNumber}</CardTitle>
                <Badge className={`${getStatusColor(invoice.status)} text-white`}>
                  {getStatusText(invoice.status)}
                </Badge>
              </div>
              <p className="text-sm text-gray-600">{invoice.customerName}</p>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-1 gap-2 text-sm">
                <div>
                  <span className="text-gray-500">Total:</span>
                  <p className="font-bold text-lg text-green-600">
                    Rp {invoice.total.toLocaleString('id-ID')}
                  </p>
                </div>
                <div>
                  <span className="text-gray-500">Tanggal:</span>
                  <p className="font-medium">
                    {new Date(invoice.issueDate).toLocaleDateString('id-ID')}
                  </p>
                </div>
                <div>
                  <span className="text-gray-500">Jatuh Tempo:</span>
                  <p className="font-medium">
                    {new Date(invoice.dueDate).toLocaleDateString('id-ID')}
                  </p>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-2 pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewInvoice(invoice)}
                  className="flex items-center gap-1"
                >
                  <Eye className="w-4 h-4" />
                  Detail
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEditInvoice(invoice)}
                  className="flex items-center gap-1"
                >
                  <Edit className="w-4 h-4" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.print()}
                  className="flex items-center gap-1"
                >
                  <Printer className="w-4 h-4" />
                  Print
                </Button>
              </div>
              
              {invoice.status !== 'paid' && (
                <div className="flex gap-2 pt-2">
                  <Button
                    size="sm"
                    onClick={() => onUpdateStatus(invoice.id, 'paid')}
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    Tandai Lunas
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
      
      {invoices.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">Belum ada invoice</p>
        </div>
      )}
    </div>
  );
};

export default InvoiceList;
