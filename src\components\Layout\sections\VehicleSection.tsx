import React from 'react';
import VehicleList from '@/components/Vehicles/VehicleList';
import VehicleForm from '@/components/Vehicles/VehicleForm';
import VehicleDetail from '@/components/Vehicles/VehicleDetail';
import { Vehicle } from '@/types/vehicle';

interface VehicleSectionProps {
  showVehicleForm: boolean;
  currentView: string;
  selectedVehicle: Vehicle | null;
  user: { name: string; role: 'admin' | 'financial' | 'operational' | 'driver' };
  vehicles: Vehicle[];
  onCreateVehicle: (vehicleData: any) => void;
  onViewVehicle: (vehicle: Vehicle) => void;
  onEditVehicle: (vehicle: Vehicle) => void;
  onToggleVehicleStatus: (vehicleId: string) => void;
  onShowVehicleForm: () => void;
  onCancelVehicleForm: () => void;
  onUpdateVehicle: (vehicleData: any) => void;
  onBackToVehicleList: () => void;
}

const VehicleSection = ({
  showVehicleForm,
  currentView,
  selectedVehicle,
  user,
  vehicles,
  onCreateVehicle,
  onViewVehicle,
  onEditVehicle,
  onToggleVehicleStatus,
  onShowVehicleForm,
  onCancelVehicleForm,
  onUpdateVehicle,
  onBackToVehicleList,
}: VehicleSectionProps) => {
  // Handle vehicle form display
  if (showVehicleForm) {
    return (
      <div className="max-w-full overflow-auto">
        <VehicleForm
          vehicle={currentView === 'vehicle-edit' ? selectedVehicle : null}
          onSubmit={currentView === 'vehicle-edit' ? onUpdateVehicle : onCreateVehicle}
          onCancel={currentView === 'vehicle-edit' ? onBackToVehicleList : onCancelVehicleForm}
          isEdit={currentView === 'vehicle-edit'}
        />
      </div>
    );
  }

  // Handle vehicle detail view
  if (currentView === 'vehicle-detail' && selectedVehicle) {
    return (
      <div className="max-w-full overflow-auto">
        <VehicleDetail
          vehicle={selectedVehicle}
          onBack={onBackToVehicleList}
          onEdit={() => onEditVehicle(selectedVehicle)}
        />
      </div>
    );
  }

  // Default vehicle list view
  return (
    <div className="max-w-full">
      <div className="space-y-4 sm:space-y-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div className="space-y-1">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Kelola Kendaraan</h1>
            <p className="text-sm sm:text-base text-gray-600">Kelola semua kendaraan dalam sistem</p>
          </div>
          {(user.role === 'admin' || user.role === 'operational') && (
            <button
              onClick={onShowVehicleForm}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium text-sm sm:text-base w-full sm:w-auto"
            >
              + Tambah Kendaraan Baru
            </button>
          )}
        </div>
        <VehicleList
          vehicles={vehicles}
          onViewVehicle={onViewVehicle}
          onEditVehicle={onEditVehicle}
          onToggleStatus={onToggleVehicleStatus}
        />
      </div>
    </div>
  );
};

export default VehicleSection;
