
import { Driver } from '@/types/driver';

export const mockDrivers: Driver[] = [
  {
    id: 'DRV-001',
    name: '<PERSON><PERSON>',
    email: 'budi.santo<PERSON>@email.com',
    phone: '+62812-3456-7890',
    licenseNumber: 'A 1234 AB',
    licenseExpiry: new Date('2025-06-15'),
    address: 'Jl. Merdeka No. 123, Jakarta Pusat',
    status: 'active',
    driverType: 'internal',
    vehicleTypes: ['Truk Kecil', '<PERSON>'],
    rating: 4.8,
    totalDeliveries: 234,
    joinDate: new Date('2023-01-15'),
    emergencyContact: {
      name: '<PERSON><PERSON>',
      phone: '+62813-9876-5432',
      relation: '<PERSON><PERSON>'
    }
  },
  {
    id: 'DRV-002',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+62811-2345-6789',
    licenseNumber: 'B 5678 CD',
    licenseExpiry: new Date('2024-12-30'),
    address: 'J<PERSON>. <PERSON><PERSON><PERSON> No. 456, <PERSON><PERSON><PERSON>',
    status: 'active',
    driverType: 'vendor',
    vehicleTypes: ['Truk Sedang', 'Pick Up'],
    rating: 4.6,
    totalDeliveries: 189,
    joinDate: new Date('2023-03-20'),
    emergencyContact: {
      name: 'Ratna Wijaya',
      phone: '+62814-1234-5678',
      relation: 'Ibu'
    }
  },
  {
    id: 'DRV-003',
    name: 'Sari Indah',
    email: '<EMAIL>',
    phone: '+62815-3456-7890',
    licenseNumber: 'A 9012 EF',
    licenseExpiry: new Date('2025-03-10'),
    address: 'Jl. Gatot Subroto No. 789, Tangerang',
    status: 'inactive',
    driverType: 'internal',
    vehicleTypes: ['Van', 'Mobil Box'],
    rating: 4.9,
    totalDeliveries: 156,
    joinDate: new Date('2023-05-10'),
    emergencyContact: {
      name: 'Ahmad Indah',
      phone: '+62816-9876-5432',
      relation: 'Suami'
    }
  },
  {
    id: 'DRV-004',
    name: 'Rudi Hartono',
    email: '<EMAIL>',
    phone: '+62817-4567-8901',
    licenseNumber: 'B 3456 GH',
    licenseExpiry: new Date('2024-08-20'),
    address: 'Jl. Thamrin No. 321, Jakarta Selatan',
    status: 'active',
    driverType: 'vendor',
    vehicleTypes: ['Truk Besar', 'Container'],
    rating: 4.7,
    totalDeliveries: 298,
    joinDate: new Date('2022-11-05'),
    emergencyContact: {
      name: 'Dewi Hartono',
      phone: '+62818-1111-2222',
      relation: 'Istri'
    }
  },
  {
    id: 'DRV-005',
    name: 'Joko Susilo',
    email: '<EMAIL>',
    phone: '+62819-5555-1111',
    licenseNumber: 'A 7890 IJ',
    licenseExpiry: new Date('2025-09-15'),
    address: 'Jl. Ahmad Yani No. 555, Surabaya',
    status: 'active',
    driverType: 'vendor',
    vehicleTypes: ['Truk Kecil', 'Van', 'Pick Up'],
    rating: 4.5,
    totalDeliveries: 142,
    joinDate: new Date('2023-06-01'),
    emergencyContact: {
      name: 'Endang Susilo',
      phone: '+62820-1111-2222',
      relation: 'Istri'
    }
  },
  {
    id: 'DRV-006',
    name: 'Agus Prasetyo',
    email: '<EMAIL>',
    phone: '+62821-6666-3333',
    licenseNumber: 'B 2468 KL',
    licenseExpiry: new Date('2025-11-20'),
    address: 'Jl. Diponegoro No. 888, Bandung',
    status: 'active',
    driverType: 'vendor',
    vehicleTypes: ['Truk Sedang', 'Truk Besar'],
    rating: 4.8,
    totalDeliveries: 267,
    joinDate: new Date('2022-08-15'),
    emergencyContact: {
      name: 'Sri Prasetyo',
      phone: '+62822-3333-4444',
      relation: 'Istri'
    }
  },
  {
    id: 'DRV-007',
    name: 'Bambang Utomo',
    email: '<EMAIL>',
    phone: '+62823-7777-4444',
    licenseNumber: 'A 1357 MN',
    licenseExpiry: new Date('2025-04-30'),
    address: 'Jl. Hayam Wuruk No. 999, Semarang',
    status: 'active',
    driverType: 'vendor',
    vehicleTypes: ['Van', 'Mobil Box'],
    rating: 4.4,
    totalDeliveries: 98,
    joinDate: new Date('2023-11-10'),
    emergencyContact: {
      name: 'Wati Utomo',
      phone: '+62824-4444-5555',
      relation: 'Istri'
    }
  }
];
