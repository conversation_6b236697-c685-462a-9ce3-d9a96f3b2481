
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Edit3 } from 'lucide-react';
import CustomerComboBox from './CustomerComboBox';
import { mockCustomers } from '@/data/mockCustomers';
import { Customer } from '@/types/customer';
import { OrderFormData } from '@/hooks/useOrderForm';

interface CustomerSectionProps {
  selectedCustomer: Customer | null;
  isManualEdit: boolean;
  formData: OrderFormData;
  isCustomerInfoReadOnly: boolean;
  onCustomerSelect: (customer: Customer | null) => void;
  onManualEdit: () => void;
  onUpdateFormData: (field: keyof OrderFormData, value: any) => void;
}

const CustomerSection = ({
  selectedCustomer,
  isManualEdit,
  formData,
  isCustomerInfoReadOnly,
  onCustomerSelect,
  onManualEdit,
  onUpdateFormData
}: CustomerSectionProps) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Informasi Pelanggan</h3>
        {isCustomerInfoReadOnly && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onManualEdit}
            className="flex items-center gap-2"
          >
            <Edit3 className="h-4 w-4" />
            Edit Manual
          </Button>
        )}
      </div>
      
      <div className="grid gap-4 md:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="customerName">Nama Perusahaan *</Label>
          <CustomerComboBox
            customers={mockCustomers}
            value={selectedCustomer?.id}
            onSelect={onCustomerSelect}
            placeholder="Pilih atau cari perusahaan..."
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="customerPhone">No. Telepon *</Label>
          <Input
            id="customerPhone"
            value={formData.customerPhone}
            onChange={(e) => onUpdateFormData('customerPhone', e.target.value)}
            required
            placeholder="021-12345678"
            readOnly={isCustomerInfoReadOnly}
            className={isCustomerInfoReadOnly ? "bg-gray-50 border-gray-200" : ""}
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="customerAddress">Alamat Perusahaan *</Label>
        <Textarea
          id="customerAddress"
          value={formData.customerAddress}
          onChange={(e) => onUpdateFormData('customerAddress', e.target.value)}
          required
          placeholder="Alamat lengkap perusahaan"
          rows={2}
          readOnly={isCustomerInfoReadOnly}
          className={isCustomerInfoReadOnly ? "bg-gray-50 border-gray-200" : ""}
        />
      </div>
      
      {isCustomerInfoReadOnly && (
        <div className="text-sm text-blue-600 bg-blue-50 p-3 rounded-md">
          ℹ️ Informasi pelanggan diisi otomatis dari database. Klik "Edit Manual" untuk mengubah.
        </div>
      )}
    </div>
  );
};

export default CustomerSection;
