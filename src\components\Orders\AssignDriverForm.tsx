
import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Truck, Loader2 } from 'lucide-react';
import DriverSelectionList from './DriverSelectionList';
import DriverAssignmentConfirmDialog from './DriverAssignmentConfirmDialog';
import { getDriverTypeBadge } from '@/lib/badgeUtils';

interface AssignDriverFormProps {
  isOpen: boolean;
  order: any;
  onClose: () => void;
  onAssign: (driverData: any) => void;
}

// Mock driver data
const availableDrivers = [
  { id: '1', name: '<PERSON><PERSON>', phone: '081234567890', vehicle: 'Truck Box', experience: '5 tahun', driverType: 'internal' },
  { id: '2', name: '<PERSON><PERSON><PERSON>', phone: '081234567891', vehicle: 'Truck Engkel', experience: '3 tahun', driverType: 'vendor' },
  { id: '3', name: 'Sari Indah', phone: '081234567892', vehicle: 'Pickup', experience: '4 tahun', driverType: 'internal' },
  { id: '4', name: 'Joko Susilo', phone: '081234567893', vehicle: 'Truck Box', experience: '7 tahun', driverType: 'vendor' },
];

const AssignDriverForm = ({ isOpen, order, onClose, onAssign }: AssignDriverFormProps) => {
  const [selectedDriverId, setSelectedDriverId] = useState('');
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);



  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedDriverId) return;
    setShowConfirmDialog(true);
  };

  const handleConfirmAssignment = async () => {
    const driver = availableDrivers.find(d => d.id === selectedDriverId);
    if (driver) {
      setIsLoading(true);
      
      // Simulate API call delay
      setTimeout(() => {
        onAssign({
          driverId: driver.id,
          driverName: driver.name,
          driverPhone: driver.phone,
          assignedAt: new Date(),
          notes: notes
        });
        
        // Reset form
        setSelectedDriverId('');
        setNotes('');
        setIsLoading(false);
        setShowConfirmDialog(false);
      }, 1000);
    }
  };

  const handleClose = () => {
    if (isLoading) return; // Prevent closing during loading
    setSelectedDriverId('');
    setNotes('');
    setShowConfirmDialog(false);
    onClose();
  };

  const handleCancelConfirmation = () => {
    setShowConfirmDialog(false);
  };

  const selectedDriverData = availableDrivers.find(d => d.id === selectedDriverId);

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col p-0">
          <DialogHeader className="px-6 pt-6 pb-2">
            <DialogTitle className="flex items-center gap-2">
              <Truck className="h-5 w-5" />
              Tugaskan Driver
            </DialogTitle>
            <DialogDescription>
              Pilih driver untuk pesanan {order?.orderNumber} - {order?.customerName}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="flex flex-col flex-1 min-h-0">
            <ScrollArea className="flex-1 px-6">
              <div className="space-y-6 pb-4">
                <div className="space-y-2">
                  <Label htmlFor="notes">Catatan Khusus (Opsional)</Label>
                  <Textarea
                    id="notes"
                    placeholder="Tambahkan catatan atau instruksi khusus untuk driver..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    disabled={isLoading}
                    className="min-h-[80px]"
                  />
                </div>
              </div>
            </ScrollArea>

            <DialogFooter className="px-6 py-4 border-t bg-gray-50/50 mt-auto">
              <div className="flex flex-col-reverse sm:flex-row gap-2 w-full sm:w-auto">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={handleClose}
                  disabled={isLoading}
                  className="w-full sm:w-auto"
                >
                  Batal
                </Button>
                <Button 
                  type="submit" 
                  disabled={!selectedDriverId || isLoading}
                  className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Memproses...
                    </>
                  ) : (
                    'Konfirmasi Penugasan'
                  )}
                </Button>
              </div>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <DriverAssignmentConfirmDialog
        isOpen={showConfirmDialog}
        driver={selectedDriverData || null}
        orderNumber={order?.orderNumber || ''}
        notes={notes}
        isLoading={isLoading}
        onConfirm={handleConfirmAssignment}
        onCancel={handleCancelConfirmation}
      />
    </>
  );
};

export default AssignDriverForm;
