
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import PhotoUploadComponent from './PhotoUploadComponent';
import { DeliveryReportFormData } from '@/types/deliveryReport';
import { useToast } from '@/hooks/use-toast';

interface VendorDocumentDeliveryFormProps {
  orderId: string;
  orderNumber: string;
  onSubmit: (data: DeliveryReportFormData) => void;
  onCancel: () => void;
}

const VendorDocumentDeliveryForm = ({ orderId, orderNumber, onSubmit, onCancel }: VendorDocumentDeliveryFormProps) => {
  const [formData, setFormData] = useState<DeliveryReportFormData>({
    orderId,
    driverType: 'vendor',
    courierName: '',
    deliveryDate: new Date(),
    receiptPhoto: undefined
  });

  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.courierName) {
      toast({
        title: "Form tidak lengkap",
        description: "Mohon isi nama kurir",
        variant: "destructive"
      });
      return;
    }

    if (!formData.receiptPhoto) {
      toast({
        title: "Form tidak lengkap",
        description: "Mohon upload foto resi pengiriman",
        variant: "destructive"
      });
      return;
    }

    onSubmit(formData);
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold">
            Lapor Pengiriman - Tahap 3: Pengiriman Surat Jalan
          </CardTitle>
          <p className="text-sm text-gray-600">
            Pesanan: {orderNumber}
          </p>
          <div className="flex items-center space-x-2 text-sm">
            <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center">✓</div>
            <span className="text-green-600">Muat</span>
            <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center">✓</div>
            <span className="text-green-600">Bongkar</span>
            <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center">3</div>
            <span className="font-medium">Kirim Dokumen</span>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="courierName">Nama Kurir</Label>
              <Input
                id="courierName"
                type="text"
                value={formData.courierName || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  courierName: e.target.value
                }))}
                placeholder="Masukkan nama kurir pengiriman surat jalan"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="deliveryDate">Tanggal Pengiriman</Label>
              <Input
                id="deliveryDate"
                type="date"
                value={formData.deliveryDate?.toISOString().split('T')[0] || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  deliveryDate: new Date(e.target.value)
                }))}
                required
              />
            </div>

            <PhotoUploadComponent
              label="Foto Resi Pengiriman"
              maxFiles={1}
              acceptVideo={false}
              files={formData.receiptPhoto ? [formData.receiptPhoto] : []}
              onFilesChange={(files) => setFormData(prev => ({
                ...prev,
                receiptPhoto: files[0] || undefined
              }))}
              required
            />

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button type="submit" className="flex-1">
                Selesaikan Laporan Pengiriman
              </Button>
              <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
                Batal
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default VendorDocumentDeliveryForm;
