
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Vehicle } from '@/types/vehicle';
import { Edit, Eye, Power } from 'lucide-react';

interface VehicleListProps {
  vehicles: Vehicle[];
  onViewVehicle: (vehicle: Vehicle) => void;
  onEditVehicle: (vehicle: Vehicle) => void;
  onToggleStatus: (vehicleId: string) => void;
}

const VehicleList = ({ vehicles, onViewVehicle, onEditVehicle, onToggleStatus }: VehicleListProps) => {
  const getStatusColor = (status: Vehicle['status']) => {
    switch (status) {
      case 'available': return 'bg-green-500';
      case 'in-transit': return 'bg-blue-500';
      case 'maintenance': return 'bg-yellow-500';
      case 'out-of-service': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusText = (status: Vehicle['status']) => {
    switch (status) {
      case 'available': return 'Tersedia';
      case 'in-transit': return '<PERSON><PERSON>';
      case 'maintenance': return 'Maintenance';
      case 'out-of-service': return 'Tidak Beroperasi';
      default: return status;
    }
  };

  // Add a function to get vehicle type badge
  const getVehicleTypeBadge = (type: Vehicle['type']) => {
    switch (type) {
      case 'Truk Kecil':
        return <Badge className="bg-amber-100 text-amber-800">Truk Kecil</Badge>;
      case 'Truk Sedang':
        return <Badge className="bg-orange-100 text-orange-800">Truk Sedang</Badge>;
      case 'Truk Besar':
        return <Badge className="bg-red-100 text-red-800">Truk Besar</Badge>;
      case 'Van':
        return <Badge className="bg-green-100 text-green-800">Van</Badge>;
      case 'Pick Up':
        return <Badge className="bg-blue-100 text-blue-800">Pick Up</Badge>;
      case 'Mobil Box':
        return <Badge className="bg-indigo-100 text-indigo-800">Mobil Box</Badge>;
      case 'Container':
        return <Badge className="bg-purple-100 text-purple-800">Container</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  // Add a function to get vehicle source badge
  const getVehicleSourceBadge = (source: Vehicle['vehicleSource']) => {
    switch (source) {
      case 'internal':
        return <Badge className="bg-blue-100 text-blue-800">Internal</Badge>;
      case 'vendor':
        return <Badge className="bg-purple-100 text-purple-800">Vendor</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {vehicles.map((vehicle) => (
          <Card key={vehicle.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg font-bold">{vehicle.plateNumber}</CardTitle>
                <Badge className={`${getStatusColor(vehicle.status)} text-white`}>
                  {getStatusText(vehicle.status)}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-600">{vehicle.brand} {vehicle.model} ({vehicle.year})</p>
              </div>
              <div className="flex gap-2 mt-2">
                {getVehicleTypeBadge(vehicle.type)}
                {getVehicleSourceBadge(vehicle.vehicleSource)}
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm">
                    <span className="text-gray-500">Kapasitas:</span> {vehicle.capacity.toLocaleString()} kg
                  </p>
                  <p className="text-sm">
                    <span className="text-gray-500">Bahan Bakar:</span> {vehicle.fuelType}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button size="sm" variant="ghost" onClick={() => onViewVehicle(vehicle)}>
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="ghost" onClick={() => onEditVehicle(vehicle)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button 
                    size="sm" 
                    variant={vehicle.status === 'available' ? 'destructive' : 'default'}
                    onClick={() => onToggleStatus(vehicle.id)}
                  >
                    <Power className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default VehicleList;
