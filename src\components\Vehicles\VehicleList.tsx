
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Vehicle } from '@/types/vehicle';
import { Edit, Eye, Power } from 'lucide-react';
import { getVehicleTypeBadge, getVehicleSourceBadge, getVehicleStatusColor, getVehicleStatusText } from '@/lib/badgeUtils';

interface VehicleListProps {
  vehicles: Vehicle[];
  onViewVehicle: (vehicle: Vehicle) => void;
  onEditVehicle: (vehicle: Vehicle) => void;
  onToggleStatus: (vehicleId: string) => void;
}

const VehicleList = ({ vehicles, onViewVehicle, onEditVehicle, onToggleStatus }: VehicleListProps) => {

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {vehicles.map((vehicle) => (
          <Card key={vehicle.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg font-bold">{vehicle.plateNumber}</CardTitle>
                <Badge className={`${getVehicleStatusColor(vehicle.status)} text-white`}>
                  {getVehicleStatusText(vehicle.status)}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <p className="text-sm text-gray-600">{vehicle.brand} {vehicle.model} ({vehicle.year})</p>
              </div>
              <div className="flex gap-2 mt-2">
                {getVehicleTypeBadge(vehicle.type)}
                {getVehicleSourceBadge(vehicle.vehicleSource)}
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm">
                    <span className="text-gray-500">Kapasitas:</span> {vehicle.capacity.toLocaleString()} kg
                  </p>
                  <p className="text-sm">
                    <span className="text-gray-500">Bahan Bakar:</span> {vehicle.fuelType}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button size="sm" variant="ghost" onClick={() => onViewVehicle(vehicle)}>
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="ghost" onClick={() => onEditVehicle(vehicle)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button 
                    size="sm" 
                    variant={vehicle.status === 'available' ? 'destructive' : 'default'}
                    onClick={() => onToggleStatus(vehicle.id)}
                  >
                    <Power className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default VehicleList;
