
import { useState, useMemo } from 'react';
import { ReportStats, ReportFilters, OrderReportData, DriverReportData, VehicleReportData } from '@/types/report';
import { Order } from '@/types/order';
import { Driver } from '@/types/driver';
import { Vehicle } from '@/types/vehicle';

export const useReportData = (orders: Order[], drivers: Driver[], vehicles: Vehicle[]) => {
  const [filters, setFilters] = useState<ReportFilters>({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    endDate: new Date()
  });

  const stats = useMemo((): ReportStats => {
    const filteredOrders = orders.filter(order => {
      const orderDate = new Date(order.pickupDate);
      return orderDate >= filters.startDate && orderDate <= filters.endDate;
    });

    return {
      totalOrders: filteredOrders.length,
      completedOrders: filteredOrders.filter(o => o.status === 'completed').length,
      pendingOrders: filteredOrders.filter(o => o.status === 'pending').length,
      totalRevenue: filteredOrders.reduce((sum, o) => sum + o.amount, 0),
      totalDrivers: drivers.length,
      activeDrivers: drivers.filter(d => d.status === 'active').length,
      totalVehicles: vehicles.length,
      availableVehicles: vehicles.filter(v => v.status === 'available').length
    };
  }, [orders, drivers, vehicles, filters]);

  const orderReportData = useMemo((): OrderReportData[] => {
    const filteredOrders = orders.filter(order => {
      const orderDate = new Date(order.pickupDate);
      return orderDate >= filters.startDate && orderDate <= filters.endDate;
    });

    const groupedData: { [key: string]: OrderReportData } = {};
    
    filteredOrders.forEach(order => {
      const date = new Date(order.pickupDate).toISOString().split('T')[0];
      if (!groupedData[date]) {
        groupedData[date] = {
          date,
          orders: 0,
          revenue: 0,
          completed: 0,
          pending: 0
        };
      }
      
      groupedData[date].orders += 1;
      groupedData[date].revenue += order.amount;
      if (order.status === 'completed') groupedData[date].completed += 1;
      if (order.status === 'pending') groupedData[date].pending += 1;
    });

    return Object.values(groupedData).sort((a, b) => a.date.localeCompare(b.date));
  }, [orders, filters]);

  const driverReportData = useMemo((): DriverReportData[] => {
    return drivers.map(driver => {
      const driverOrders = orders.filter(o => o.assignedDriver === driver.name);
      const filteredOrders = driverOrders.filter(order => {
        const orderDate = new Date(order.pickupDate);
        return orderDate >= filters.startDate && orderDate <= filters.endDate;
      });

      return {
        driverId: driver.id,
        name: driver.name,
        totalOrders: filteredOrders.length,
        completedOrders: filteredOrders.filter(o => o.status === 'completed').length,
        rating: driver.rating,
        revenue: filteredOrders.reduce((sum, o) => sum + o.amount, 0)
      };
    });
  }, [drivers, orders, filters]);

  const vehicleReportData = useMemo((): VehicleReportData[] => {
    return vehicles.map(vehicle => {
      // Since Order doesn't have assignedVehicleId, we'll use a simplified approach
      // In a real app, you'd have this relationship properly defined
      const vehicleOrders = orders.filter(order => {
        // For now, we'll use a mock relationship based on order index
        const orderIndex = orders.indexOf(order);
        const vehicleIndex = vehicles.indexOf(vehicle);
        return orderIndex % vehicles.length === vehicleIndex;
      });
      
      const filteredOrders = vehicleOrders.filter(order => {
        const orderDate = new Date(order.pickupDate);
        return orderDate >= filters.startDate && orderDate <= filters.endDate;
      });

      return {
        vehicleId: vehicle.id,
        plateNumber: vehicle.plateNumber,
        type: vehicle.type,
        totalOrders: filteredOrders.length,
        utilizationRate: filteredOrders.length > 0 ? 75 : 0, // Mock utilization rate
        maintenanceCost: Math.random() * 5000000 // Mock maintenance cost
      };
    });
  }, [vehicles, orders, filters]);

  return {
    stats,
    orderReportData,
    driverReportData,
    vehicleReportData,
    filters,
    setFilters
  };
};
