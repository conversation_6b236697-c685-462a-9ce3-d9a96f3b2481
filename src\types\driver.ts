
export interface Driver {
  id: string;
  name: string;
  email: string;
  phone: string;
  licenseNumber: string;
  licenseExpiry: Date;
  address: string;
  status: 'active' | 'inactive' | 'suspended';
  driverType: 'internal' | 'vendor';
  vehicleTypes: string[];
  rating: number;
  totalDeliveries: number;
  joinDate: Date;
  profileImage?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relation: string;
  };
}

export interface DriverFormData {
  name: string;
  email: string;
  phone: string;
  licenseNumber: string;
  licenseExpiry: Date;
  address: string;
  driverType: 'internal' | 'vendor';
  vehicleTypes: string[];
  emergencyContact?: {
    name: string;
    phone: string;
    relation: string;
  };
}
