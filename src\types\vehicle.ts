
export interface Vehicle {
  id: string;
  plateNumber: string;
  type: 'Truk Kecil' | 'Truk Sedang' | 'Truk Besar' | 'Van' | 'Pick Up' | 'Mobil Box' | 'Container';
  vehicleSource: 'internal' | 'vendor';
  brand: string;
  model: string;
  year: number;
  capacity: number; // in kg
  status: 'available' | 'in-transit' | 'maintenance' | 'out-of-service';
  condition: 'excellent' | 'good' | 'fair' | 'poor';
  fuelType: 'Bensin' | 'Solar' | 'Listrik';
  lastMaintenance: Date;
  nextMaintenance: Date;
  mileage: number;
  assignedDriverId?: string;
  registrationExpiry: Date;
  insuranceExpiry: Date;
  notes?: string;
}

export interface VehicleFormData {
  plateNumber: string;
  type: 'Truk Kecil' | 'Truk Sedang' | 'Truk Besar' | 'Van' | 'Pick Up' | 'Mobil Box' | 'Container';
  vehicleSource: 'internal' | 'vendor';
  brand: string;
  model: string;
  year: number;
  capacity: number;
  fuelType: 'Bensin' | 'Solar' | 'Listrik';
  registrationExpiry: Date;
  insuranceExpiry: Date;
  notes?: string;
}
