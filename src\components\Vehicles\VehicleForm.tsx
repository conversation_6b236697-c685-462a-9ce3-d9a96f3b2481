
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Vehicle, VehicleFormData } from '@/types/vehicle';
import { EntitySchemas, FormValidationHelpers } from '@/lib/formValidation';
import { formatDateToISO } from '@/lib/dateUtils';

type VehicleFormValues = z.infer<typeof EntitySchemas.vehicle>;

interface VehicleFormProps {
  vehicle?: Vehicle;
  onSubmit: (data: VehicleFormData) => void;
  onCancel: () => void;
  isEdit?: boolean;
}

const VehicleForm = ({ vehicle, onSubmit, onCancel, isEdit = false }: VehicleFormProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<VehicleFormValues>({
    resolver: zodResolver(EntitySchemas.vehicle),
    defaultValues: vehicle ? {
      plateNumber: vehicle.plateNumber,
      type: vehicle.type,
      vehicleSource: vehicle.vehicleSource,
      brand: vehicle.brand,
      model: vehicle.model,
      year: vehicle.year,
      capacity: vehicle.capacity,
      fuelType: vehicle.fuelType,
      registrationExpiry: formatDateToISO(vehicle.registrationExpiry),
      insuranceExpiry: formatDateToISO(vehicle.insuranceExpiry),
      notes: vehicle.notes || ''
    } : {
      type: 'Truk Kecil',
      vehicleSource: 'internal',
      year: new Date().getFullYear(),
      capacity: 0,
      fuelType: 'Solar',
    }
  });

  const onFormSubmit = (data: VehicleFormValues) => {
    const formattedData = {
      ...data,
      registrationExpiry: new Date(data.registrationExpiry),
      insuranceExpiry: new Date(data.insuranceExpiry),
    };
    onSubmit(formattedData);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{isEdit ? 'Edit Kendaraan' : 'Tambah Kendaraan Baru'}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="plateNumber">Nomor Plat *</Label>
                <Input
                  id="plateNumber"
                  {...register('plateNumber')}
                  placeholder="B 1234 ABC"
                />
                {errors.plateNumber && (
                  <p className="text-sm text-red-600 mt-1">{errors.plateNumber.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="vehicleSource">Sumber Kendaraan *</Label>
                <Select value={watch('vehicleSource')} onValueChange={(value) => setValue('vehicleSource', value as any)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih sumber kendaraan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="internal">Internal</SelectItem>
                    <SelectItem value="vendor">Vendor</SelectItem>
                  </SelectContent>
                </Select>
                {errors.vehicleSource && (
                  <p className="text-sm text-red-600 mt-1">{errors.vehicleSource.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="type">Jenis Kendaraan *</Label>
                <Select value={watch('type')} onValueChange={(value) => setValue('type', value as any)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih jenis kendaraan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Truk Kecil">Truk Kecil</SelectItem>
                    <SelectItem value="Truk Sedang">Truk Sedang</SelectItem>
                    <SelectItem value="Truk Besar">Truk Besar</SelectItem>
                    <SelectItem value="Van">Van</SelectItem>
                    <SelectItem value="Pick Up">Pick Up</SelectItem>
                    <SelectItem value="Mobil Box">Mobil Box</SelectItem>
                    <SelectItem value="Container">Container</SelectItem>
                  </SelectContent>
                </Select>
                {errors.type && (
                  <p className="text-sm text-red-600 mt-1">{errors.type.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="brand">Merek *</Label>
                <Input
                  id="brand"
                  {...register('brand')}
                  placeholder="Mitsubishi"
                />
                {errors.brand && (
                  <p className="text-sm text-red-600 mt-1">{errors.brand.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="model">Model *</Label>
                <Input
                  id="model"
                  {...register('model')}
                  placeholder="Colt Diesel"
                />
                {errors.model && (
                  <p className="text-sm text-red-600 mt-1">{errors.model.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="year">Tahun *</Label>
                <Input
                  id="year"
                  type="number"
                  {...register('year', { valueAsNumber: true })}
                  placeholder="2020"
                />
                {errors.year && (
                  <p className="text-sm text-red-600 mt-1">{errors.year.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="capacity">Kapasitas (kg) *</Label>
                <Input
                  id="capacity"
                  type="number"
                  {...register('capacity', { valueAsNumber: true })}
                  placeholder="3000"
                />
                {errors.capacity && (
                  <p className="text-sm text-red-600 mt-1">{errors.capacity.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="fuelType">Bahan Bakar *</Label>
                <Select value={watch('fuelType')} onValueChange={(value) => setValue('fuelType', value as any)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih bahan bakar" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Bensin">Bensin</SelectItem>
                    <SelectItem value="Solar">Solar</SelectItem>
                    <SelectItem value="Listrik">Listrik</SelectItem>
                    <SelectItem value="Hybrid">Hybrid</SelectItem>
                  </SelectContent>
                </Select>
                {errors.fuelType && (
                  <p className="text-sm text-red-600 mt-1">{errors.fuelType.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="registrationExpiry">STNK Berlaku Hingga *</Label>
                <Input
                  id="registrationExpiry"
                  type="date"
                  {...register('registrationExpiry')}
                />
                {errors.registrationExpiry && (
                  <p className="text-sm text-red-600 mt-1">{errors.registrationExpiry.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="insuranceExpiry">Asuransi Berlaku Hingga *</Label>
                <Input
                  id="insuranceExpiry"
                  type="date"
                  {...register('insuranceExpiry')}
                />
                {errors.insuranceExpiry && (
                  <p className="text-sm text-red-600 mt-1">{errors.insuranceExpiry.message}</p>
                )}
              </div>
            </div>

            <div className="mt-6">
              <Label htmlFor="notes">Catatan</Label>
              <Textarea
                id="notes"
                {...register('notes')}
                placeholder="Tambahkan catatan tentang kendaraan ini..."
                rows={4}
              />
              {errors.notes && (
                <p className="text-sm text-red-600 mt-1">{errors.notes.message}</p>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Batal
          </Button>
          <Button type="submit">
            {isEdit ? 'Simpan Perubahan' : 'Tambah Kendaraan'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default VehicleForm;
