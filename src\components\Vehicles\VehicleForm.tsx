
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Vehicle, VehicleFormData } from '@/types/vehicle';

interface VehicleFormProps {
  vehicle?: Vehicle;
  onSubmit: (data: VehicleFormData) => void;
  onCancel: () => void;
  isEdit?: boolean;
}

const VehicleForm = ({ vehicle, onSubmit, onCancel, isEdit = false }: VehicleFormProps) => {
  const [formData, setFormData] = useState<VehicleFormData>({
    plateNumber: '',
    type: 'Truk Kecil',
    vehicleSource: 'internal',
    brand: '',
    model: '',
    year: new Date().getFullYear(),
    capacity: 0,
    fuelType: 'Solar',
    registrationExpiry: new Date(),
    insuranceExpiry: new Date(),
    notes: ''
  });

  useEffect(() => {
    if (vehicle && isEdit) {
      setFormData({
        plateNumber: vehicle.plateNumber,
        type: vehicle.type,
        vehicleSource: vehicle.vehicleSource,
        brand: vehicle.brand,
        model: vehicle.model,
        year: vehicle.year,
        capacity: vehicle.capacity,
        fuelType: vehicle.fuelType,
        registrationExpiry: vehicle.registrationExpiry,
        insuranceExpiry: vehicle.insuranceExpiry,
        notes: vehicle.notes || ''
      });
    }
  }, [vehicle, isEdit]);

  const handleInputChange = (field: keyof VehicleFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{isEdit ? 'Edit Kendaraan' : 'Tambah Kendaraan Baru'}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="plateNumber">Nomor Plat *</Label>
                <Input
                  id="plateNumber"
                  required
                  value={formData.plateNumber}
                  onChange={(e) => handleInputChange('plateNumber', e.target.value)}
                  placeholder="B 1234 ABC"
                />
              </div>

              <div>
                <Label htmlFor="vehicleSource">Sumber Kendaraan *</Label>
                <Select 
                  value={formData.vehicleSource} 
                  onValueChange={(value) => handleInputChange('vehicleSource', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih sumber kendaraan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="internal">Internal</SelectItem>
                    <SelectItem value="vendor">Vendor</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="type">Jenis Kendaraan *</Label>
                <Select 
                  value={formData.type} 
                  onValueChange={(value) => handleInputChange('type', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih jenis kendaraan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Truk Kecil">Truk Kecil</SelectItem>
                    <SelectItem value="Truk Sedang">Truk Sedang</SelectItem>
                    <SelectItem value="Truk Besar">Truk Besar</SelectItem>
                    <SelectItem value="Van">Van</SelectItem>
                    <SelectItem value="Pick Up">Pick Up</SelectItem>
                    <SelectItem value="Mobil Box">Mobil Box</SelectItem>
                    <SelectItem value="Container">Container</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="brand">Merek *</Label>
                <Input
                  id="brand"
                  required
                  value={formData.brand}
                  onChange={(e) => handleInputChange('brand', e.target.value)}
                  placeholder="Mitsubishi"
                />
              </div>

              <div>
                <Label htmlFor="model">Model *</Label>
                <Input
                  id="model"
                  required
                  value={formData.model}
                  onChange={(e) => handleInputChange('model', e.target.value)}
                  placeholder="Colt Diesel"
                />
              </div>

              <div>
                <Label htmlFor="year">Tahun *</Label>
                <Input
                  id="year"
                  type="number"
                  required
                  value={formData.year}
                  onChange={(e) => handleInputChange('year', parseInt(e.target.value))}
                  placeholder="2020"
                />
              </div>

              <div>
                <Label htmlFor="capacity">Kapasitas (kg) *</Label>
                <Input
                  id="capacity"
                  type="number"
                  required
                  value={formData.capacity}
                  onChange={(e) => handleInputChange('capacity', parseInt(e.target.value))}
                  placeholder="3000"
                />
              </div>

              <div>
                <Label htmlFor="fuelType">Bahan Bakar *</Label>
                <Select 
                  value={formData.fuelType} 
                  onValueChange={(value) => handleInputChange('fuelType', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih bahan bakar" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Bensin">Bensin</SelectItem>
                    <SelectItem value="Solar">Solar</SelectItem>
                    <SelectItem value="Listrik">Listrik</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="registrationExpiry">STNK Berlaku Hingga *</Label>
                <Input
                  id="registrationExpiry"
                  type="date"
                  required
                  value={formData.registrationExpiry instanceof Date 
                    ? formData.registrationExpiry.toISOString().split('T')[0] 
                    : new Date(formData.registrationExpiry).toISOString().split('T')[0]}
                  onChange={(e) => handleInputChange('registrationExpiry', new Date(e.target.value))}
                />
              </div>

              <div>
                <Label htmlFor="insuranceExpiry">Asuransi Berlaku Hingga *</Label>
                <Input
                  id="insuranceExpiry"
                  type="date"
                  required
                  value={formData.insuranceExpiry instanceof Date 
                    ? formData.insuranceExpiry.toISOString().split('T')[0] 
                    : new Date(formData.insuranceExpiry).toISOString().split('T')[0]}
                  onChange={(e) => handleInputChange('insuranceExpiry', new Date(e.target.value))}
                />
              </div>
            </div>

            <div className="mt-6">
              <Label htmlFor="notes">Catatan</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Tambahkan catatan tentang kendaraan ini..."
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Batal
          </Button>
          <Button type="submit">
            {isEdit ? 'Simpan Perubahan' : 'Tambah Kendaraan'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default VehicleForm;
