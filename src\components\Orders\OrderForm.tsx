
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useOrderForm } from '@/hooks/useOrderForm';
import CustomerSection from './CustomerSection';
import ShippingSection from './ShippingSection';
import CargoSection from './CargoSection';

interface OrderFormProps {
  onSubmit: (orderData: any) => void;
  onCancel: () => void;
  initialOrder?: any;
  isEdit?: boolean;
}

const OrderForm = ({ onSubmit, onCancel, initialOrder, isEdit = false }: OrderFormProps) => {
  const {
    selectedCustomer,
    isManualEdit,
    formData,
    handleCustomerSelect,
    handleManualEdit,
    updateFormData,
    isCustomerInfoReadOnly
  } = useOrderForm(initialOrder);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const orderData = {
      ...formData,
      customerId: selectedCustomer?.id,
      orderNumber: initialOrder?.orderNumber || `ORD-${Date.now()}`,
      status: initialOrder?.status || 'pending',
      createdAt: initialOrder?.createdAt || new Date(),
      amount: initialOrder?.amount || 0,
      assignedDriver: formData.assignedDriver || initialOrder?.assignedDriver,
      // Pastikan semua field dari initialOrder dipertahankan jika tidak diubah
      ...initialOrder && {
        id: initialOrder.id,
        assignedAt: initialOrder.assignedAt,
        driverNotes: initialOrder.driverNotes
      }
    };
    onSubmit(orderData);
  };

  // Tambahkan bagian untuk menampilkan informasi status dan driver jika dalam mode edit
  const renderAdditionalInfo = () => {
    if (!isEdit || !initialOrder) return null;
    
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 p-4 bg-gray-50 rounded-md">
        <div className="space-y-2">
          <Label>Status Pesanan</Label>
          <div className="font-medium">{initialOrder.status}</div>
        </div>
        {initialOrder.assignedDriver && (
          <div className="space-y-2">
            <Label>Driver</Label>
            <div className="font-medium">{initialOrder.assignedDriver}</div>
          </div>
        )}
        {initialOrder.assignedAt && (
          <div className="space-y-2">
            <Label>Tanggal Penugasan</Label>
            <div className="font-medium">
              {new Date(initialOrder.assignedAt).toLocaleDateString('id-ID')}
            </div>
          </div>
        )}
        {initialOrder.driverNotes && (
          <div className="space-y-2 md:col-span-2">
            <Label>Catatan Driver</Label>
            <div className="font-medium">{initialOrder.driverNotes}</div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <Card className="w-full">
        <CardHeader className="space-y-2">
          <CardTitle className="text-lg sm:text-xl">{isEdit ? 'Edit Pesanan' : 'Buat Pesanan Baru'}</CardTitle>
          <CardDescription className="text-sm">
            {isEdit 
              ? 'Ubah informasi pesanan transportasi' 
              : 'Isi formulir di bawah untuk membuat pesanan transportasi baru'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <ScrollArea className="h-[calc(100vh-280px)] w-full">
            <form onSubmit={handleSubmit} className="space-y-6 pr-4">
              {isEdit && (
                <div className="mb-4">
                  <h3 className="text-lg font-semibold">Nomor Pesanan: {initialOrder?.orderNumber}</h3>
                </div>
              )}
              
              {renderAdditionalInfo()}
              
              <CustomerSection
                selectedCustomer={selectedCustomer}
                isManualEdit={isManualEdit}
                formData={formData}
                isCustomerInfoReadOnly={isCustomerInfoReadOnly}
                onCustomerSelect={handleCustomerSelect}
                onManualEdit={handleManualEdit}
                onUpdateFormData={updateFormData}
              />

              <ShippingSection
                formData={formData}
                onUpdateFormData={updateFormData}
              />

              <CargoSection
                formData={formData}
                onUpdateFormData={updateFormData}
              />
              
              {/* Tambahkan bagian untuk prioritas */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Prioritas Pesanan</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="priority-normal"
                      name="priority"
                      value="normal"
                      checked={formData.priority === 'normal'}
                      onChange={() => updateFormData('priority', 'normal')}
                      className="h-4 w-4 text-blue-600"
                    />
                    <Label htmlFor="priority-normal">Normal</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="priority-high"
                      name="priority"
                      value="high"
                      checked={formData.priority === 'high'}
                      onChange={() => updateFormData('priority', 'high')}
                      className="h-4 w-4 text-blue-600"
                    />
                    <Label htmlFor="priority-high">Tinggi</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="priority-urgent"
                      name="priority"
                      value="urgent"
                      checked={formData.priority === 'urgent'}
                      onChange={() => updateFormData('priority', 'urgent')}
                      className="h-4 w-4 text-blue-600"
                    />
                    <Label htmlFor="priority-urgent">Urgent</Label>
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-4 pt-4">
                <Button type="button" variant="outline" onClick={onCancel}>
                  Batal
                </Button>
                <Button type="submit">
                  {isEdit ? 'Simpan Perubahan' : 'Buat Pesanan'}
                </Button>
              </div>
            </form>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};

export default OrderForm;
