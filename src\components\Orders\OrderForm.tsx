
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useOrderForm } from '@/hooks/useOrderForm';
import CustomerSection from './CustomerSection';
import ShippingSection from './ShippingSection';
import CargoSection from './CargoSection';

interface OrderFormProps {
  onSubmit: (orderData: any) => void;
  onCancel: () => void;
  initialOrder?: any;
  isEdit?: boolean;
}

const OrderForm = ({ onSubmit, onCancel, initialOrder, isEdit = false }: OrderFormProps) => {
  const {
    selectedCustomer,
    isManualEdit,
    formData,
    handleCustomerSelect,
    handleManualEdit,
    updateFormData,
    isCustomerInfoReadOnly
  } = useOrderForm(initialOrder);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const orderData = {
      ...formData,
      customerId: selectedCustomer?.id,
      orderNumber: initialOrder?.orderNumber || `ORD-${Date.now()}`,
      status: initialOrder?.status || 'pending',
      createdAt: initialOrder?.createdAt || new Date(),
      amount: initialOrder?.amount || 0
    };
    onSubmit(orderData);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <Card className="w-full">
        <CardHeader className="space-y-2">
          <CardTitle className="text-lg sm:text-xl">{isEdit ? 'Edit Pesanan' : 'Buat Pesanan Baru'}</CardTitle>
          <CardDescription className="text-sm">
            {isEdit 
              ? 'Ubah informasi pesanan transportasi' 
              : 'Isi formulir di bawah untuk membuat pesanan transportasi baru'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <ScrollArea className="h-[calc(100vh-280px)] w-full">
            <form onSubmit={handleSubmit} className="space-y-6 pr-4">
              <CustomerSection
                selectedCustomer={selectedCustomer}
                isManualEdit={isManualEdit}
                formData={formData}
                isCustomerInfoReadOnly={isCustomerInfoReadOnly}
                onCustomerSelect={handleCustomerSelect}
                onManualEdit={handleManualEdit}
                onUpdateFormData={updateFormData}
              />

              <ShippingSection
                formData={formData}
                onUpdateFormData={updateFormData}
              />

              <CargoSection
                formData={formData}
                onUpdateFormData={updateFormData}
              />

              <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t">
                <Button type="submit" className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto">
                  {isEdit ? 'Update Pesanan' : 'Buat Pesanan'}
                </Button>
                <Button type="button" variant="outline" onClick={onCancel} className="w-full sm:w-auto">
                  Batal
                </Button>
              </div>
            </form>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};

export default OrderForm;
