
import React from 'react';

interface MobileHoverZoneProps {
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  isVisible: boolean;
}

const MobileHoverZone = ({ onMouseEnter, onMouseLeave, isVisible }: MobileHoverZoneProps) => {
  return (
    <>
      {/* Hover trigger zone - only visible on mobile when sidebar is hidden */}
      <div
        className={`fixed top-0 left-0 w-6 h-full z-40 md:hidden ${
          isVisible ? 'hidden' : 'block'
        }`}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      />
      
      {/* Visual hint - small bar on the left edge */}
      <div
        className={`fixed top-1/2 left-0 w-1 h-12 bg-blue-600/50 rounded-r-md transform -translate-y-1/2 z-30 transition-opacity duration-300 md:hidden ${
          isVisible ? 'opacity-0' : 'opacity-60'
        }`}
      />
    </>
  );
};

export default MobileHoverZone;
