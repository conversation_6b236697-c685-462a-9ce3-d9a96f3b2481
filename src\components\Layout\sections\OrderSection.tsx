import React from 'react';
import OrderForm from '@/components/Orders/OrderForm';
import OrderList from '@/components/Orders/OrderList';
import OrderDetail from '@/components/Orders/OrderDetail';
import AssignDriverForm from '@/components/Orders/AssignDriverForm';
import { Order } from '@/types/order';

interface OrderSectionProps {
  activeTab: string;
  showOrderForm: boolean;
  currentView: string;
  selectedOrder: Order | null;
  showAssignDriverModal: boolean;
  user: { name: string; role: 'admin' | 'financial' | 'operational' | 'driver' };
  orders: Order[];
  onCreateOrder: (orderData: any) => void;
  onCancelOrderForm: () => void;
  onShowOrderForm: () => void;
  onViewOrder: (order: Order) => void;
  onEditOrder: (order: Order) => void;
  onAssignDriver: (order: Order) => void;
  onBackToList: () => void;
  onCloseAssignDriverModal: () => void;
  onAssignDriverComplete: (driverData: any) => void;
  onUpdateOrder: (orderData: any) => void;
}

const OrderSection = ({
  activeTab,
  showOrderForm,
  currentView,
  selectedOrder,
  showAssignDriverModal,
  user,
  orders,
  onCreateOrder,
  onCancelOrderForm,
  onShowOrderForm,
  onViewOrder,
  onEditOrder,
  onAssignDriver,
  onBackToList,
  onCloseAssignDriverModal,
  onAssignDriverComplete,
  onUpdateOrder,
}: OrderSectionProps) => {
  // Handle order form display
  if (showOrderForm) {
    return (
      <div className="max-w-full overflow-auto">
        <OrderForm
          onSubmit={onCreateOrder}
          onCancel={onCancelOrderForm}
        />
      </div>
    );
  }

  // Handle order detail view
  if (currentView === 'order-detail' && selectedOrder) {
    return (
      <div className="max-w-full overflow-auto">
        <OrderDetail
          order={selectedOrder}
          onBack={onBackToList}
        />
      </div>
    );
  }

  // Handle order edit view
  if (currentView === 'order-edit' && selectedOrder) {
    return (
      <div className="max-w-full overflow-auto">
        <OrderForm
          onSubmit={onUpdateOrder}
          onCancel={onBackToList}
          initialOrder={selectedOrder}
          isEdit={true}
        />
      </div>
    );
  }

  // Default order list view
  return (
    <div className="max-w-full">
      <div className="space-y-4 sm:space-y-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div className="space-y-1">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
              {activeTab === 'my-orders' ? 'Pesanan Saya' : 'Kelola Pesanan'}
            </h1>
            <p className="text-sm sm:text-base text-gray-600">
              {activeTab === 'my-orders' ? 'Lihat pesanan yang ditugaskan kepada Anda' : 'Kelola semua pesanan transportasi'}
            </p>
          </div>
          {(user.role === 'admin' || user.role === 'operational') && (
            <button
              onClick={onShowOrderForm}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium text-sm sm:text-base w-full sm:w-auto"
            >
              + Buat Pesanan Baru
            </button>
          )}
        </div>
        <OrderList
          orders={orders}
          userRole={user.role}
          onViewOrder={onViewOrder}
          onEditOrder={onEditOrder}
          onAssignDriver={onAssignDriver}
        />
      </div>
      
      {/* Assign Driver Modal */}
      <AssignDriverForm
        isOpen={showAssignDriverModal}
        order={selectedOrder}
        onClose={onCloseAssignDriverModal}
        onAssign={onAssignDriverComplete}
      />
    </div>
  );
};

export default OrderSection;

