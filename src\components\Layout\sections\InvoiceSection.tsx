import React from 'react';
import InvoiceList from '@/components/Invoices/InvoiceList';
import InvoiceForm from '@/components/Invoices/InvoiceForm';
import InvoiceDetail from '@/components/Invoices/InvoiceDetail';
import { Invoice } from '@/types/invoice';

interface InvoiceSectionProps {
  showInvoiceForm: boolean;
  currentView: string;
  selectedInvoice: Invoice | null;
  user: { name: string; role: 'admin' | 'financial' | 'operational' | 'driver' };
  invoices: Invoice[];
  onCreateInvoice: (invoiceData: any) => void;
  onViewInvoice: (invoice: Invoice) => void;
  onEditInvoice: (invoice: Invoice) => void;
  onUpdateInvoiceStatus: (invoiceId: string, status: Invoice['status']) => void;
  onShowInvoiceForm: () => void;
  onCancelInvoiceForm: () => void;
  onUpdateInvoice: (invoiceData: any) => void;
  onBackToInvoiceList: () => void;
}

const InvoiceSection = ({
  showInvoiceForm,
  currentView,
  selectedInvoice,
  user,
  invoices,
  onCreateInvoice,
  onViewInvoice,
  onEditInvoice,
  onUpdateInvoiceStatus,
  onShowInvoiceForm,
  onCancelInvoiceForm,
  onUpdateInvoice,
  onBackToInvoiceList,
}: InvoiceSectionProps) => {
  // Handle invoice form display
  if (showInvoiceForm) {
    return (
      <div className="max-w-full overflow-auto">
        <InvoiceForm
          invoice={currentView === 'invoice-edit' ? selectedInvoice : null}
          onSubmit={currentView === 'invoice-edit' ? onUpdateInvoice : onCreateInvoice}
          onCancel={currentView === 'invoice-edit' ? onBackToInvoiceList : onCancelInvoiceForm}
          isEdit={currentView === 'invoice-edit'}
        />
      </div>
    );
  }

  // Handle invoice detail view
  if (currentView === 'invoice-detail' && selectedInvoice) {
    return (
      <div className="max-w-full overflow-auto">
        <InvoiceDetail
          invoice={selectedInvoice}
          onBack={onBackToInvoiceList}
          onEdit={() => onEditInvoice(selectedInvoice)}
        />
      </div>
    );
  }

  // Default invoice list view
  return (
    <div className="max-w-full">
      <div className="space-y-4 sm:space-y-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div className="space-y-1">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Kelola Invoice</h1>
            <p className="text-sm sm:text-base text-gray-600">Kelola semua invoice dalam sistem</p>
          </div>
          {(user.role === 'admin' || user.role === 'financial') && (
            <button
              onClick={onShowInvoiceForm}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium text-sm sm:text-base w-full sm:w-auto"
            >
              + Buat Invoice Baru
            </button>
          )}
        </div>
        <InvoiceList
          invoices={invoices}
          onViewInvoice={onViewInvoice}
          onEditInvoice={onEditInvoice}
          onUpdateStatus={onUpdateInvoiceStatus}
        />
      </div>
    </div>
  );
};

export default InvoiceSection;
