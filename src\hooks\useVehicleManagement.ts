
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Vehicle, VehicleFormData } from '@/types/vehicle';
import { mockVehicles } from '@/data/mockVehicles';

export const useVehicleManagement = () => {
  const [vehicles, setVehicles] = useState<Vehicle[]>(mockVehicles);
  const { toast } = useToast();

  const handleCreateVehicle = (vehicleData: VehicleFormData) => {
    const newVehicle: Vehicle = {
      ...vehicleData,
      id: `VEH-${String(vehicles.length + 1).padStart(3, '0')}`,
      status: 'available',
      condition: 'good',
      lastMaintenance: new Date(),
      nextMaintenance: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 3 months from now
      mileage: 0
    };
    
    setVehicles(prev => [newVehicle, ...prev]);
    toast({
      title: "Kendaraan berhasil ditambahkan",
      description: `${newVehicle.plateNumber} telah ditambahkan ke sistem`,
    });
  };

  const handleUpdateVehicle = (vehicleId: string, updatedData: Partial<VehicleFormData>) => {
    setVehicles(prev =>
      prev.map(vehicle =>
        vehicle.id === vehicleId
          ? { ...vehicle, ...updatedData }
          : vehicle
      )
    );
    toast({
      title: "Kendaraan berhasil diperbarui",
      description: "Informasi kendaraan telah diperbarui",
    });
  };

  const handleToggleVehicleStatus = (vehicleId: string) => {
    setVehicles(prev =>
      prev.map(vehicle =>
        vehicle.id === vehicleId
          ? { 
              ...vehicle, 
              status: vehicle.status === 'available' ? 'out-of-service' : 'available'
            }
          : vehicle
      )
    );
    
    const vehicle = vehicles.find(v => v.id === vehicleId);
    if (vehicle) {
      toast({
        title: "Status kendaraan diperbarui",
        description: `${vehicle.plateNumber} sekarang ${vehicle.status === 'available' ? 'tidak tersedia' : 'tersedia'}`,
      });
    }
  };

  const handleDeleteVehicle = (vehicleId: string) => {
    const vehicle = vehicles.find(v => v.id === vehicleId);
    setVehicles(prev => prev.filter(v => v.id !== vehicleId));
    
    if (vehicle) {
      toast({
        title: "Kendaraan dihapus",
        description: `${vehicle.plateNumber} telah dihapus dari sistem`,
      });
    }
  };

  return {
    vehicles,
    handleCreateVehicle,
    handleUpdateVehicle,
    handleToggleVehicleStatus,
    handleDeleteVehicle
  };
};
