import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Driver, DriverFormData } from '@/types/driver';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Save, X } from 'lucide-react';

const driverSchema = z.object({
  name: z.string().min(2, 'Nama minimal 2 karakter'),
  email: z.string().email('Email tidak valid'),
  phone: z.string().min(10, 'Nomor telepon minimal 10 digit'),
  licenseNumber: z.string().min(5, 'Nomor SIM minimal 5 karakter'),
  licenseExpiry: z.string().min(1, 'Tanggal kadaluarsa SIM wajib diisi'),
  address: z.string().min(10, 'Alamat minimal 10 karakter'),
  driverType: z.enum(['internal', 'vendor'], { required_error: 'Jenis driver wajib dipilih' }),
  vehicleTypes: z.string().min(1, 'Jenis kendaraan wajib diisi'),
  emergencyContactName: z.string().optional(),
  emergencyContactPhone: z.string().optional(),
  emergencyContactRelation: z.string().optional(),
});

type DriverFormValues = z.infer<typeof driverSchema>;

interface DriverFormProps {
  driver?: Driver | null;
  onSubmit: (data: DriverFormData) => void;
  onCancel: () => void;
  isEdit?: boolean;
}

const DriverForm = ({ driver, onSubmit, onCancel, isEdit = false }: DriverFormProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<DriverFormValues>({
    resolver: zodResolver(driverSchema),
    defaultValues: driver ? {
      name: driver.name,
      email: driver.email,
      phone: driver.phone,
      licenseNumber: driver.licenseNumber,
      licenseExpiry: driver.licenseExpiry.toISOString().split('T')[0],
      address: driver.address,
      driverType: driver.driverType,
      vehicleTypes: driver.vehicleTypes.join(', '),
      emergencyContactName: driver.emergencyContact?.name || '',
      emergencyContactPhone: driver.emergencyContact?.phone || '',
      emergencyContactRelation: driver.emergencyContact?.relation || '',
    } : {}
  });

  const onFormSubmit = (data: DriverFormValues) => {
    const formData: DriverFormData = {
      name: data.name,
      email: data.email,
      phone: data.phone,
      licenseNumber: data.licenseNumber,
      licenseExpiry: new Date(data.licenseExpiry),
      address: data.address,
      driverType: data.driverType,
      vehicleTypes: data.vehicleTypes.split(',').map(type => type.trim()),
      emergencyContact: data.emergencyContactName ? {
        name: data.emergencyContactName,
        phone: data.emergencyContactPhone || '',
        relation: data.emergencyContactRelation || ''
      } : undefined
    };
    
    onSubmit(formData);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" onClick={onCancel} className="flex items-center gap-2">
          <ArrowLeft className="w-4 h-4" />
          <span className="hidden sm:inline">Kembali</span>
        </Button>
        <h1 className="text-2xl sm:text-3xl font-bold">
          {isEdit ? 'Edit Driver' : 'Tambah Driver Baru'}
        </h1>
      </div>

      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Informasi Pribadi</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Nama Lengkap *</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="Masukkan nama lengkap"
                />
                {errors.name && (
                  <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
                )}
              </div>
              
              <div>
                <Label htmlFor="phone">Nomor Telepon *</Label>
                <Input
                  id="phone"
                  {...register('phone')}
                  placeholder="+62812-3456-7890"
                />
                {errors.phone && (
                  <p className="text-sm text-red-600 mt-1">{errors.phone.message}</p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                {...register('email')}
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="text-sm text-red-600 mt-1">{errors.email.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="driverType">Jenis Driver *</Label>
              <Select
                value={watch('driverType')}
                onValueChange={(value) => setValue('driverType', value as 'internal' | 'vendor')}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih jenis driver" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="internal">Driver Internal</SelectItem>
                  <SelectItem value="vendor">Driver Vendor</SelectItem>
                </SelectContent>
              </Select>
              {errors.driverType && (
                <p className="text-sm text-red-600 mt-1">{errors.driverType.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="address">Alamat *</Label>
              <Textarea
                id="address"
                {...register('address')}
                placeholder="Masukkan alamat lengkap"
                rows={3}
              />
              {errors.address && (
                <p className="text-sm text-red-600 mt-1">{errors.address.message}</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Informasi SIM & Kendaraan</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="licenseNumber">Nomor SIM *</Label>
                <Input
                  id="licenseNumber"
                  {...register('licenseNumber')}
                  placeholder="A 1234 AB"
                />
                {errors.licenseNumber && (
                  <p className="text-sm text-red-600 mt-1">{errors.licenseNumber.message}</p>
                )}
              </div>
              
              <div>
                <Label htmlFor="licenseExpiry">Tanggal Kadaluarsa SIM *</Label>
                <Input
                  id="licenseExpiry"
                  type="date"
                  {...register('licenseExpiry')}
                />
                {errors.licenseExpiry && (
                  <p className="text-sm text-red-600 mt-1">{errors.licenseExpiry.message}</p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="vehicleTypes">Jenis Kendaraan yang Bisa Dikendarai *</Label>
              <Input
                id="vehicleTypes"
                {...register('vehicleTypes')}
                placeholder="Truk Kecil, Van, Pick Up (pisahkan dengan koma)"
              />
              {errors.vehicleTypes && (
                <p className="text-sm text-red-600 mt-1">{errors.vehicleTypes.message}</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Kontak Darurat (Opsional)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="emergencyContactName">Nama</Label>
                <Input
                  id="emergencyContactName"
                  {...register('emergencyContactName')}
                  placeholder="Nama kontak darurat"
                />
              </div>
              
              <div>
                <Label htmlFor="emergencyContactPhone">Nomor Telepon</Label>
                <Input
                  id="emergencyContactPhone"
                  {...register('emergencyContactPhone')}
                  placeholder="+62813-9876-5432"
                />
              </div>
              
              <div>
                <Label htmlFor="emergencyContactRelation">Hubungan</Label>
                <Input
                  id="emergencyContactRelation"
                  {...register('emergencyContactRelation')}
                  placeholder="Istri, Suami, Orang Tua, dll"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex gap-4 justify-end">
          <Button type="button" variant="outline" onClick={onCancel} className="flex items-center gap-2">
            <X className="w-4 h-4" />
            Batal
          </Button>
          <Button type="submit" className="flex items-center gap-2">
            <Save className="w-4 h-4" />
            {isEdit ? 'Perbarui Driver' : 'Tambah Driver'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default DriverForm;
