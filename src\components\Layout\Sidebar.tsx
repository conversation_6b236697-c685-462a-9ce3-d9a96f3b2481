
import React from 'react';
import { 
  Home, 
  FileText, 
  Truck, 
  Users, 
  DollarSign, 
  BarChart3, 
  Calendar,
  ClipboardList 
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';

interface SidebarProps {
  userRole: 'admin' | 'financial' | 'operational' | 'driver';
  activeTab: string;
  onTabChange: (tab: string) => void;
  isVisible?: boolean;
  onMouseLeave?: () => void;
}

const Sidebar = ({ userRole, activeTab, onTabChange, isVisible = true, onMouseLeave }: SidebarProps) => {
  const isMobile = useIsMobile();

  const getMenuItems = () => {
    const common = [
      { id: 'dashboard', label: 'Dashboard', icon: Home }
    ];

    switch (userRole) {
      case 'admin':
        return [
          ...common,
          { id: 'orders', label: '<PERSON><PERSON><PERSON>', icon: FileText },
          { id: 'drivers', label: '<PERSON><PERSON><PERSON>', icon: Users },
          { id: 'vehicles', label: '<PERSON><PERSON><PERSON>', icon: Truck },
          { id: 'invoices', label: 'Invoice', icon: DollarSign },
          { id: 'reports', label: 'Laporan', icon: BarChart3 }
        ];
      
      case 'financial':
        return [
          ...common,
          { id: 'orders', label: 'Lihat Pesanan', icon: FileText },
          { id: 'invoices', label: 'Invoice', icon: DollarSign },
          { id: 'reports', label: '<PERSON>poran Keuangan', icon: BarChart3 }
        ];
      
      case 'operational':
        return [
          ...common,
          { id: 'orders', label: 'Kelola Pesanan', icon: FileText },
          { id: 'drivers', label: 'Status Driver', icon: Users },
          { id: 'daily-ops', label: 'Operasional Harian', icon: Calendar }
        ];
      
      case 'driver':
        return [
          ...common,
          { id: 'my-orders', label: 'Pesanan Saya', icon: ClipboardList },
          { id: 'delivery-report', label: 'Lapor Pengiriman', icon: FileText }
        ];
      
      default:
        return common;
    }
  };

  const menuItems = getMenuItems();

  return (
    <>
      {/* Mobile backdrop */}
      {isMobile && isVisible && (
        <div 
          className="fixed inset-0 bg-black/20 z-40 md:hidden"
          onClick={onMouseLeave}
        />
      )}
      
      {/* Sidebar */}
      <div 
        className={`
          ${isMobile ? 'fixed' : 'relative'} 
          top-0 left-0 h-full z-50
          w-64 bg-gray-50 border-r border-gray-200
          transform transition-transform duration-300 ease-in-out
          ${isMobile && !isVisible ? '-translate-x-full' : 'translate-x-0'}
          ${!isMobile ? 'block' : ''}
        `}
        onMouseLeave={onMouseLeave}
      >
        <div className="p-4">
          <nav className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              return (
                <Button
                  key={item.id}
                  variant={activeTab === item.id ? "default" : "ghost"}
                  className={`w-full justify-start text-sm ${
                    activeTab === item.id 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => onTabChange(item.id)}
                >
                  <Icon className="mr-2 h-4 w-4" />
                  <span className="truncate">{item.label}</span>
                </Button>
              );
            })}
          </nav>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
