
import React from 'react';
import { 
  Home, 
  FileText, 
  Truck, 
  Users, 
  DollarSign, 
  BarChart3, 
  Calendar,
  ClipboardList 
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';

interface SidebarProps {
  userRole: 'admin' | 'financial' | 'operational' | 'driver';
  driverType?: 'internal' | 'vendor';
  activeTab: string;
  onTabChange: (tab: string) => void;
  onLoadingReportClick?: () => void;
  isVisible?: boolean;
  onMouseLeave?: () => void;
}

const Sidebar = ({ 
  userRole, 
  driverType = 'internal',
  activeTab, 
  onTabChange,
  onLoadingReportClick,
  isVisible = true, 
  onMouseLeave 
}: SidebarProps) => {
  const isMobile = useIsMobile();

  const getMenuItems = () => {
    const common = [
      { id: 'dashboard', label: 'Dashboard', icon: Home }
    ];

    switch (userRole) {
      case 'admin':
        return [
          ...common,
          { id: 'orders', label: '<PERSON><PERSON><PERSON>', icon: ClipboardList },
          { id: 'customers', label: 'Pelanggan', icon: Users },
          { id: 'drivers', label: 'Driver', icon: Users },
          { id: 'vehicles', label: 'Kendaraan', icon: Truck },
          { id: 'invoices', label: 'Invoice', icon: DollarSign },
          { id: 'reports', label: 'Laporan', icon: BarChart3 }
        ];
      
      case 'financial':
        return [
          ...common,
          { id: 'invoices', label: 'Invoice', icon: DollarSign },
          { id: 'reports', label: 'Laporan Keuangan', icon: BarChart3 }
        ];
      
      case 'operational':
        return [
          ...common,
          { id: 'orders', label: 'Pesanan', icon: ClipboardList },
          { id: 'drivers', label: 'Driver', icon: Users },
          { id: 'vehicles', label: 'Kendaraan', icon: Truck },
          { id: 'delivery-reports', label: 'Laporan Pengiriman', icon: FileText }
        ];
      
      case 'driver':
        return [
          ...common,
          { id: 'my-orders', label: 'Pesanan Saya', icon: ClipboardList },
          { id: 'loading-report', label: 'Lapor Muat', icon: FileText },
          ...(driverType === 'vendor' ? [{ id: 'unloading-report', label: 'Lapor Bongkar', icon: FileText }] : [])
        ];
      
      default:
        return common;
    }
  };

  const menuItems = getMenuItems();

  const handleMenuItemClick = (itemId: string) => {
    if (itemId === 'loading-report' && onLoadingReportClick) {
      onLoadingReportClick();
    } else {
      onTabChange(itemId);
    }
  };

  return (
    <>
      {/* Mobile backdrop */}
      {isMobile && isVisible && (
        <div 
          className="fixed inset-0 bg-black/20 z-40 md:hidden"
          onClick={onMouseLeave}
        />
      )}
      
      {/* Sidebar */}
      <div 
        className={`
          ${isMobile ? 'fixed' : 'relative'} 
          top-0 left-0 h-full z-50
          w-64 bg-gray-50 border-r border-gray-200
          transform transition-transform duration-300 ease-in-out
          ${isMobile && !isVisible ? '-translate-x-full' : 'translate-x-0'}
          ${!isMobile ? 'block' : ''}
        `}
        onMouseLeave={onMouseLeave}
      >
        <div className="p-4">
          <nav className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              return (
                <Button
                  key={item.id}
                  variant={activeTab === item.id ? "default" : "ghost"}
                  className={`w-full justify-start text-sm ${
                    activeTab === item.id 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => handleMenuItemClick(item.id)}
                >
                  <Icon className="mr-2 h-4 w-4" />
                  <span className="truncate">{item.label}</span>
                </Button>
              );
            })}
          </nav>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
