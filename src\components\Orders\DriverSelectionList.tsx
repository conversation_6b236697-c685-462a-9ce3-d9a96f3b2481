
import React from 'react';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { User } from 'lucide-react';

interface Driver {
  id: string;
  name: string;
  phone: string;
  vehicle: string;
  experience: string;
}

interface DriverSelectionListProps {
  drivers: Driver[];
  selectedDriver: string;
  onDriverSelect: (driverId: string) => void;
  disabled?: boolean;
}

const DriverSelectionList = ({ 
  drivers, 
  selectedDriver, 
  onDriverSelect,
  disabled = false 
}: DriverSelectionListProps) => {
  return (
    <div className="space-y-4">
      <Label className="text-base font-semibold">Pilih Driver yang Tersedia</Label>
      <RadioGroup value={selectedDriver} onValueChange={onDriverSelect}>
        {drivers.map((driver) => (
          <div key={driver.id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
            <RadioGroupItem value={driver.id} id={driver.id} disabled={disabled} />
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-500 flex-shrink-0" />
                <Label htmlFor={driver.id} className="font-medium cursor-pointer truncate">
                  {driver.name}
                </Label>
              </div>
              <div className="text-sm text-gray-600 mt-1 space-y-1">
                <p className="truncate">Telepon: {driver.phone}</p>
                <p className="truncate">Kendaraan: {driver.vehicle} • Pengalaman: {driver.experience}</p>
              </div>
            </div>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
};

export default DriverSelectionList;
