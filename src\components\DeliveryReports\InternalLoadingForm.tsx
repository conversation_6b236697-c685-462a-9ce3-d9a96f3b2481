import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DeliveryReportFormData } from '@/types/deliveryReport';
import { useToast } from '@/hooks/use-toast';
import { FileUploader } from '@/components/ui/file-uploader';

interface InternalLoadingFormProps {
  orderId: string;
  orderNumber: string;
  onSubmit: (data: DeliveryReportFormData) => void;
  onCancel: () => void;
}

// Mock customers for dropdown
const mockCustomers = [
  { id: 'cust1', name: 'PT. Maju Jaya' },
  { id: 'cust2', name: 'CV. Abadi <PERSON>' },
  { id: 'cust3', name: 'PT. <PERSON>ks<PERSON>' },
  { id: 'cust4', name: 'UD. Bar<PERSON>' },
  { id: 'cust5', name: 'PT. Sejahtera Bersama' }
];

// Mock destination companies
const mockCompanies = [
  'PT. Logistik Prima',
  'CV. Express Delivery',
  'PT. Fast Courier',
  'UD. Reliable Transport',
  'PT. Swift Logistics'
];

// Mock destination areas
const mockAreas = [
  'Jakarta Pusat',
  'Jakarta Barat',
  'Jakarta Timur',
  'Jakarta Selatan',
  'Jakarta Utara',
  'Tangerang',
  'Bekasi',
  'Depok',
  'Bogor'
];

const InternalLoadingForm = ({ orderId, orderNumber, onSubmit, onCancel }: InternalLoadingFormProps) => {
  const [formData, setFormData] = useState<DeliveryReportFormData>({
    orderId,
    driverType: 'internal',
    pickupDate: new Date(),
    customerName: '',
    destinationCompany: '',
    destinationArea: '',
    waybillNumber: '',
    waybillPhotos: []
  });

  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.customerName || !formData.waybillNumber || !formData.destinationCompany || formData.waybillPhotos?.length === 0) {
      toast({
        title: "Form tidak lengkap",
        description: "Mohon isi semua field yang diperlukan dan unggah foto surat jalan",
        variant: "destructive"
      });
      return;
    }

    onSubmit(formData);
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold">
            Lapor Muat - Driver Internal
          </CardTitle>
          <p className="text-sm text-gray-600">
            Pesanan: {orderNumber}
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="pickupDate">Tanggal Muat</Label>
              <Input
                id="pickupDate"
                type="date"
                value={formData.pickupDate?.toISOString().split('T')[0] || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  pickupDate: new Date(e.target.value)
                }))}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="customerName">Nama Customer</Label>
              <Select
                value={formData.customerName}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  customerName: value
                }))}
              >
                <SelectTrigger id="customerName">
                  <SelectValue placeholder="Pilih customer" />
                </SelectTrigger>
                <SelectContent>
                  {mockCustomers.map(customer => (
                    <SelectItem key={customer.id} value={customer.name}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="destinationCompany">Perusahaan Tujuan</Label>
              <Select
                value={formData.destinationCompany}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  destinationCompany: value
                }))}
              >
                <SelectTrigger id="destinationCompany">
                  <SelectValue placeholder="Pilih perusahaan tujuan" />
                </SelectTrigger>
                <SelectContent>
                  {mockCompanies.map(company => (
                    <SelectItem key={company} value={company}>
                      {company}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="destinationArea">Daerah Tujuan</Label>
              <Select
                value={formData.destinationArea}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  destinationArea: value
                }))}
              >
                <SelectTrigger id="destinationArea">
                  <SelectValue placeholder="Pilih daerah tujuan" />
                </SelectTrigger>
                <SelectContent>
                  {mockAreas.map(area => (
                    <SelectItem key={area} value={area}>
                      {area}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="waybillNumber">Nomor Surat Jalan</Label>
              <Input
                id="waybillNumber"
                value={formData.waybillNumber}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  waybillNumber: e.target.value
                }))}
                required
              />
            </div>

            <div className="space-y-2">
              <Label>Foto Surat Jalan</Label>
              <FileUploader
                accept="image/*"
                maxFiles={2}
                onFilesSelected={(files) => setFormData(prev => ({
                  ...prev,
                  waybillPhotos: files
                }))}
              />
              <p className="text-xs text-gray-500">Unggah foto surat jalan (maks. 2 foto)</p>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={onCancel}>
                Batal
              </Button>
              <Button type="submit">
                Kirim Laporan
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default InternalLoadingForm;