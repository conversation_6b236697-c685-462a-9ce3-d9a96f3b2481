
import React from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import { OrderFormData } from '@/hooks/useOrderForm';

interface ShippingSectionProps {
  formData: OrderFormData;
  onUpdateFormData: (field: keyof OrderFormData, value: any) => void;
}

const ShippingSection = ({ formData, onUpdateFormData }: ShippingSectionProps) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900">Informasi Pengiriman</h3>
      
      <div className="grid gap-4 md:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="pickupAddress">Alamat Penjemputan *</Label>
          <Textarea
            id="pickupAddress"
            value={formData.pickupAddress}
            onChange={(e) => onUpdateFormData('pickupAddress', e.target.value)}
            required
            placeholder="Alamat lengkap penjemputan"
            rows={3}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="deliveryAddress">Alamat Pengiriman *</Label>
          <Textarea
            id="deliveryAddress"
            value={formData.deliveryAddress}
            onChange={(e) => onUpdateFormData('deliveryAddress', e.target.value)}
            required
            placeholder="Alamat lengkap pengiriman"
            rows={3}
          />
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <div className="space-y-2">
          <Label>Tanggal Penjemputan *</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.pickupDate ? (
                  format(formData.pickupDate, "dd MMMM yyyy", { locale: id })
                ) : (
                  <span>Pilih tanggal penjemputan</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={formData.pickupDate}
                onSelect={(date) => onUpdateFormData('pickupDate', date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
        <div className="space-y-2">
          <Label>Tanggal Pengiriman *</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.deliveryDate ? (
                  format(formData.deliveryDate, "dd MMMM yyyy", { locale: id })
                ) : (
                  <span>Pilih tanggal pengiriman</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={formData.deliveryDate}
                onSelect={(date) => onUpdateFormData('deliveryDate', date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </div>
  );
};

export default ShippingSection;
