
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from "@/components/ui/badge";
import PhotoUploadComponent from './PhotoUploadComponent';
import { DeliveryReportFormData } from '@/types/deliveryReport';
import { useToast } from '@/hooks/use-toast';
import { getVehicleTypeBadge, getVehicleSourceBadge } from '@/lib/badgeUtils';

interface VendorLoadingFormProps {
  orderId: string;
  orderNumber: string;
  onSubmit: (data: DeliveryReportFormData) => void;
  onCancel: () => void;
}

// Mock vehicles for dropdown
const mockVehicles = [
  { id: '1', plateNumber: 'B 1234 ABC', type: 'Truk Kecil', vehicleSource: 'internal' },
  { id: '2', plateNumber: 'B 5678 DEF', type: 'Truk Sedang', vehicleSource: 'vendor' },
  { id: '3', plateNumber: 'B 9012 GHI', type: 'Truk Besar', vehicleSource: 'internal' },
  { id: '4', plateNumber: 'B 3456 JKL', type: 'Van', vehicleSource: 'vendor' },
  { id: '5', plateNumber: 'B 7890 MNO', type: 'Pick Up', vehicleSource: 'internal' }
];



const VendorLoadingForm = ({ orderId, orderNumber, onSubmit, onCancel }: VendorLoadingFormProps) => {
  const [formData, setFormData] = useState<DeliveryReportFormData>({
    orderId,
    driverType: 'vendor',
    vehicleId: '',
    pickupDate: new Date(),
    loadingPhotos: [],
    loadingVideo: undefined,
    waybillPhotosLoading: []
  });

  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.vehicleId) {
      toast({
        title: "Form tidak lengkap",
        description: "Mohon pilih kendaraan",
        variant: "destructive"
      });
      return;
    }

    if (!formData.loadingPhotos || formData.loadingPhotos.length === 0) {
      toast({
        title: "Form tidak lengkap", 
        description: "Mohon upload foto kegiatan muat",
        variant: "destructive"
      });
      return;
    }

    if (!formData.waybillPhotosLoading || formData.waybillPhotosLoading.length < 2) {
      toast({
        title: "Form tidak lengkap",
        description: "Mohon upload 2 foto surat jalan",
        variant: "destructive"
      });
      return;
    }

    onSubmit(formData);
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold">
            Lapor Pengiriman - Tahap 1: Muat Barang
          </CardTitle>
          <p className="text-sm text-gray-600">
            Pesanan: {orderNumber}
          </p>
          <div className="flex items-center space-x-2 text-sm">
            <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center">1</div>
            <span className="font-medium">Muat</span>
            <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center">2</div>
            <span className="text-gray-500">Bongkar</span>
            <div className="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center">3</div>
            <span className="text-gray-500">Kirim Dokumen</span>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="vehicleId">Kendaraan *</Label>
              <Select
                value={formData.vehicleId}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  vehicleId: value
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih kendaraan" />
                </SelectTrigger>
                <SelectContent>
                  {mockVehicles.map((vehicle) => (
                    <SelectItem key={vehicle.id} value={vehicle.id}>
                      <div className="flex items-center gap-2">
                        <span>{vehicle.plateNumber}</span>
                        <div className="flex gap-1">
                          {getVehicleTypeBadge(vehicle.type)}
                          {getVehicleSourceBadge(vehicle.vehicleSource)}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="pickupDate">Tanggal Jemput</Label>
              <Input
                id="pickupDate"
                type="date"
                value={formData.pickupDate?.toISOString().split('T')[0] || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  pickupDate: new Date(e.target.value)
                }))}
                required
              />
            </div>

            <PhotoUploadComponent
              label="Foto/Video Kegiatan Muat"
              maxFiles={5}
              acceptVideo={true}
              files={[...(formData.loadingPhotos || []), ...(formData.loadingVideo ? [formData.loadingVideo] : [])]}
              onFilesChange={(files) => {
                const videos = files.filter(f => f.type.startsWith('video/'));
                const photos = files.filter(f => f.type.startsWith('image/'));
                setFormData(prev => ({
                  ...prev,
                  loadingPhotos: photos,
                  loadingVideo: videos[0] || undefined
                }));
              }}
              required
            />

            <PhotoUploadComponent
              label="Foto Surat Jalan (2 foto)"
              maxFiles={2}
              acceptVideo={false}
              files={formData.waybillPhotosLoading || []}
              onFilesChange={(files) => setFormData(prev => ({
                ...prev,
                waybillPhotosLoading: files
              }))}
              required
            />

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button type="submit" className="flex-1">
                Laporkan Tahap Muat
              </Button>
              <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
                Batal
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default VendorLoadingForm;
