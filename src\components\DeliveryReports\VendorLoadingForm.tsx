
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DeliveryReportFormData } from '@/types/deliveryReport';
import { useToast } from '@/hooks/use-toast';
import { FileUploader } from '@/components/ui/file-uploader';

interface VendorLoadingFormProps {
  orderId: string;
  orderNumber: string;
  onSubmit: (data: DeliveryReportFormData) => void;
  onCancel: () => void;
}

const VendorLoadingForm = ({ orderId, orderNumber, onSubmit, onCancel }: VendorLoadingFormProps) => {
  const [formData, setFormData] = useState<DeliveryReportFormData>({
    orderId,
    driverType: 'vendor',
    pickupDate: new Date(),
    loadingPhotos: [],
    waybillPhotosLoading: []
  });

  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.pickupDate || formData.loadingPhotos?.length === 0 || formData.waybillPhotosLoading?.length < 2) {
      toast({
        title: "Form tidak lengkap",
        description: "Mohon isi tanggal muat, unggah foto kegiatan muat, dan 2 foto surat jalan",
        variant: "destructive"
      });
      return;
    }

    onSubmit(formData);
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold">
            Lapor Muat - Driver Vendor
          </CardTitle>
          <p className="text-sm text-gray-600">
            Pesanan: {orderNumber}
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="pickupDate">Tanggal Muat</Label>
              <Input
                id="pickupDate"
                type="date"
                value={formData.pickupDate?.toISOString().split('T')[0] || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  pickupDate: new Date(e.target.value)
                }))}
                required
              />
            </div>

            <div className="space-y-2">
              <Label>Foto Surat Jalan (2 foto)</Label>
              <FileUploader
                accept="image/*"
                maxFiles={2}
                onFilesSelected={(files) => setFormData(prev => ({
                  ...prev,
                  waybillPhotosLoading: files
                }))}
              />
              <p className="text-xs text-gray-500">Unggah 2 foto surat jalan</p>
            </div>

            <div className="space-y-2">
              <Label>Foto Kegiatan Muat</Label>
              <FileUploader
                accept="image/*"
                maxFiles={5}
                onFilesSelected={(files) => setFormData(prev => ({
                  ...prev,
                  loadingPhotos: files
                }))}
              />
              <p className="text-xs text-gray-500">Unggah foto kegiatan muat (maks. 5 foto)</p>
            </div>

            <div className="space-y-2">
              <Label>Video Kegiatan Muat (Opsional)</Label>
              <FileUploader
                accept="video/*"
                maxFiles={1}
                onFilesSelected={(files) => setFormData(prev => ({
                  ...prev,
                  loadingVideo: files[0]
                }))}
              />
              <p className="text-xs text-gray-500">Unggah video kegiatan muat (opsional)</p>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={onCancel}>
                Batal
              </Button>
              <Button type="submit">
                Kirim Laporan
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default VendorLoadingForm;
