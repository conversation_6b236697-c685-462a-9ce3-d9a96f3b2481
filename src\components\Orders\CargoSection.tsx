
import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { OrderFormData } from '@/hooks/useOrderForm';

interface CargoSectionProps {
  formData: OrderFormData;
  onUpdateFormData: (field: keyof OrderFormData, value: any) => void;
}

const CargoSection = ({ formData, onUpdateFormData }: CargoSectionProps) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900">Informasi Barang</h3>
      
      <div className="grid gap-4 md:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="cargoDescription">Deskripsi Barang *</Label>
          <Input
            id="cargoDescription"
            value={formData.cargoDescription}
            onChange={(e) => onUpdateFormData('cargoDescription', e.target.value)}
            required
            placeholder="Contoh: Electronics, Furniture, dll"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="weight">Berat Barang (kg)</Label>
          <Input
            id="weight"
            type="number"
            value={formData.weight}
            onChange={(e) => onUpdateFormData('weight', e.target.value)}
            placeholder="1000"
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="priority">Prioritas</Label>
        <Select value={formData.priority} onValueChange={(value) => onUpdateFormData('priority', value)}>
          <SelectTrigger>
            <SelectValue placeholder="Pilih prioritas" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="low">Rendah</SelectItem>
            <SelectItem value="normal">Normal</SelectItem>
            <SelectItem value="high">Tinggi</SelectItem>
            <SelectItem value="urgent">Mendesak</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="notes">Catatan Tambahan</Label>
        <Textarea
          id="notes"
          value={formData.notes}
          onChange={(e) => onUpdateFormData('notes', e.target.value)}
          placeholder="Instruksi khusus atau catatan tambahan"
          rows={3}
        />
      </div>
    </div>
  );
};

export default CargoSection;
