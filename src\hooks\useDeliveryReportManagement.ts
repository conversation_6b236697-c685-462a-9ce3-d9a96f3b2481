
import { useState } from 'react';
import { DeliveryReport, DeliveryReportFormData } from '@/types/deliveryReport';
import { useToast } from '@/hooks/use-toast';

// Mock delivery reports data with various stages for testing
const mockDeliveryReports: DeliveryReport[] = [
  {
    id: 'RPT-001',
    orderId: 'ORD-2024-001',
    orderNumber: 'ORD-2024-001',
    driverType: 'internal',
    driverId: 'DRV-001',
    driverName: '<PERSON><PERSON>',
    status: 'completed',
    createdAt: new Date('2024-12-10'),
    updatedAt: new Date('2024-12-10'),
    internalData: {
      pickupDate: new Date('2024-12-10'),
      customerName: 'PT. Elektronik Jaya',
      waybillNumber: 'WB-001-2024',
      destinationCompany: 'Jakarta Office'
    }
  },
  {
    id: 'RPT-002',
    orderId: 'ORD-2024-002',
    orderNumber: 'ORD-2024-002',
    driverType: 'vendor',
    driverId: 'DRV-002',
    driverName: '<PERSON><PERSON>',
    status: 'unloading-reported',
    createdAt: new Date('2024-12-12'),
    updatedAt: new Date('2024-12-13'),
    loadingData: {
      vehicleId: 'VEH-002',
      pickupDate: new Date('2024-12-12'),
      loadingPhotos: [],
      waybillPhotosLoading: [],
      reportedAt: new Date('2024-12-12')
    },
    unloadingData: {
      unloadingDate: new Date('2024-12-13'),
      unloadingPhotos: [],
      waybillPhotosUnloading: [],
      reportedAt: new Date('2024-12-13')
    }
  },
  {
    id: 'RPT-003',
    orderId: 'ORD-2024-008',
    orderNumber: 'ORD-2024-008',
    driverType: 'vendor',
    driverId: 'DRV-004',
    driverName: 'Rudi Hartono',
    status: 'loading-reported',
    createdAt: new Date('2024-12-19'),
    updatedAt: new Date('2024-12-19'),
    loadingData: {
      vehicleId: 'VEH-007',
      pickupDate: new Date('2024-12-19'),
      loadingPhotos: [],
      waybillPhotosLoading: [],
      reportedAt: new Date('2024-12-19')
    }
  }
];

export const useDeliveryReportManagement = () => {
  const [deliveryReports, setDeliveryReports] = useState<DeliveryReport[]>(mockDeliveryReports);
  const { toast } = useToast();

  const handleCreateDeliveryReport = (reportData: DeliveryReportFormData) => {
    const newReport: DeliveryReport = {
      id: `RPT-${String(deliveryReports.length + 1).padStart(3, '0')}`,
      orderId: reportData.orderId,
      orderNumber: reportData.orderId,
      driverType: reportData.driverType,
      driverId: 'current-driver-id', // This would come from auth context
      driverName: 'Current Driver', // This would come from auth context
      status: reportData.driverType === 'internal' ? 'completed' : 'loading-reported',
      createdAt: new Date(),
      updatedAt: new Date(),
      
      // Set data based on driver type
      ...(reportData.driverType === 'internal' && {
        internalData: {
          pickupDate: reportData.pickupDate!,
          customerName: reportData.customerName!,
          waybillNumber: reportData.waybillNumber!,
          destinationCompany: reportData.destinationCompany!
        }
      }),
      
      ...(reportData.driverType === 'vendor' && {
        loadingData: {
          vehicleId: reportData.vehicleId!,
          pickupDate: reportData.pickupDate!,
          loadingPhotos: reportData.loadingPhotos || [],
          loadingVideo: reportData.loadingVideo,
          waybillPhotosLoading: reportData.waybillPhotosLoading || [],
          reportedAt: new Date()
        }
      })
    };

    setDeliveryReports(prev => [newReport, ...prev]);
    
    toast({
      title: "Laporan berhasil dibuat",
      description: reportData.driverType === 'internal' 
        ? "Laporan pengiriman telah diselesaikan"
        : "Tahap muat telah dilaporkan. Lanjutkan ke tahap bongkar.",
    });
  };

  const handleUpdateDeliveryReport = (reportId: string, reportData: DeliveryReportFormData, stage: 'unloading' | 'document-delivery') => {
    setDeliveryReports(prev => 
      prev.map(report => {
        if (report.id === reportId) {
          const updatedReport = { ...report, updatedAt: new Date() };
          
          if (stage === 'unloading') {
            updatedReport.status = 'unloading-reported';
            updatedReport.unloadingData = {
              unloadingDate: reportData.unloadingDate!,
              unloadingPhotos: reportData.unloadingPhotos || [],
              unloadingVideo: reportData.unloadingVideo,
              waybillPhotosUnloading: reportData.waybillPhotosUnloading || [],
              reportedAt: new Date()
            };
          } else if (stage === 'document-delivery') {
            updatedReport.status = 'completed';
            updatedReport.documentDeliveryData = {
              courierName: reportData.courierName!,
              receiptPhoto: reportData.receiptPhoto!,
              deliveryDate: reportData.deliveryDate!,
              reportedAt: new Date()
            };
          }
          
          return updatedReport;
        }
        return report;
      })
    );

    toast({
      title: "Laporan berhasil diperbarui",
      description: stage === 'unloading' 
        ? "Tahap bongkar telah dilaporkan. Lanjutkan ke pengiriman dokumen."
        : "Laporan pengiriman telah diselesaikan sepenuhnya.",
    });
  };

  const getReportByOrderId = (orderId: string) => {
    return deliveryReports.find(report => report.orderId === orderId);
  };

  return {
    deliveryReports,
    handleCreateDeliveryReport,
    handleUpdateDeliveryReport,
    getReportByOrderId
  };
};
