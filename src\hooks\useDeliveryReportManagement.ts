
import { useState } from 'react';
import { DeliveryReport, DeliveryReportFormData } from '@/types/deliveryReport';
import { useToast } from '@/hooks/use-toast';

// Mock delivery reports data with various stages for testing
const mockDeliveryReports: DeliveryReport[] = [
  {
    id: 'RPT-001',
    orderId: 'ORD-2024-001',
    orderNumber: 'ORD-2024-001',
    driverType: 'internal',
    driverId: 'DRV-001',
    driverName: '<PERSON><PERSON>',
    status: 'completed',
    createdAt: new Date('2024-12-10'),
    updatedAt: new Date('2024-12-10'),
    internalData: {
      pickupDate: new Date('2024-12-10'),
      customerName: 'PT. Elektronik Jaya',
      waybillNumber: 'WB-001-2024',
      destinationCompany: 'Jakarta Office'
    }
  },
  {
    id: 'RPT-002',
    orderId: 'ORD-2024-002',
    orderNumber: 'ORD-2024-002',
    driverType: 'vendor',
    driverId: 'DRV-002',
    driverName: '<PERSON><PERSON>',
    status: 'unloading-reported',
    createdAt: new Date('2024-12-12'),
    updatedAt: new Date('2024-12-13'),
    loadingData: {
      vehicleId: 'VEH-002',
      pickupDate: new Date('2024-12-12'),
      loadingPhotos: [],
      waybillPhotosLoading: [],
      reportedAt: new Date('2024-12-12')
    },
    unloadingData: {
      unloadingDate: new Date('2024-12-13'),
      unloadingPhotos: [],
      waybillPhotosUnloading: [],
      reportedAt: new Date('2024-12-13')
    }
  },
  {
    id: 'RPT-003',
    orderId: 'ORD-2024-008',
    orderNumber: 'ORD-2024-008',
    driverType: 'vendor',
    driverId: 'DRV-004',
    driverName: 'Rudi Hartono',
    status: 'loading-reported',
    createdAt: new Date('2024-12-19'),
    updatedAt: new Date('2024-12-19'),
    loadingData: {
      vehicleId: 'VEH-007',
      pickupDate: new Date('2024-12-19'),
      loadingPhotos: [],
      waybillPhotosLoading: [],
      reportedAt: new Date('2024-12-19')
    }
  }
];

export const useDeliveryReportManagement = () => {
  const [deliveryReports, setDeliveryReports] = useState<DeliveryReport[]>(mockDeliveryReports);
  const { toast } = useToast();

  const handleCreateDeliveryReport = (reportData: DeliveryReportFormData) => {
    // Generate a new report ID
    const reportId = `RPT-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
    
    // Create a new report object
    const newReport: DeliveryReport = {
      id: reportId,
      orderId: reportData.orderId || '',
      orderNumber: reportData.orderId || '', // In a real app, you'd get this from the order
      driverType: reportData.driverType,
      driverId: 'DRV-001', // In a real app, you'd get this from the current user
      driverName: 'John Doe', // In a real app, you'd get this from the current user
      status: reportData.driverType === 'internal' ? 'completed' : 'loading-reported',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    // Add the appropriate data based on driver type
    if (reportData.driverType === 'internal') {
      newReport.internalData = {
        pickupDate: reportData.pickupDate || new Date(),
        customerName: reportData.customerName || '',
        waybillNumber: reportData.waybillNumber || '',
        destinationCompany: reportData.destinationCompany || '',
        destinationArea: reportData.destinationArea,
        waybillPhotos: reportData.waybillPhotos
      };
    } else {
      newReport.loadingData = {
        vehicleId: reportData.vehicleId || 'VEH-001',
        pickupDate: reportData.pickupDate || new Date(),
        loadingPhotos: reportData.loadingPhotos || [],
        loadingVideo: reportData.loadingVideo,
        waybillPhotosLoading: reportData.waybillPhotosLoading || [],
        reportedAt: new Date()
      };
    }
    
    // Add the new report to the state
    setDeliveryReports(prev => [newReport, ...prev]);
    
    toast({
      title: "Laporan berhasil dibuat",
      description: reportData.driverType === 'internal' 
        ? "Laporan pengiriman telah diselesaikan"
        : "Tahap muat telah dilaporkan. Lanjutkan ke tahap bongkar.",
    });
  };

  const handleUpdateDeliveryReport = (reportId: string, reportData: DeliveryReportFormData, stage: 'unloading' | 'document-delivery') => {
    setDeliveryReports(prev => 
      prev.map(report => {
        if (report.id === reportId) {
          const updatedReport = { ...report, updatedAt: new Date() };
          
          if (stage === 'unloading') {
            updatedReport.status = 'unloading-reported';
            updatedReport.unloadingData = {
              unloadingDate: reportData.unloadingDate || new Date(),
              unloadingPhotos: reportData.unloadingPhotos || [],
              unloadingVideo: reportData.unloadingVideo,
              waybillPhotosUnloading: reportData.waybillPhotosUnloading || [],
              reportedAt: new Date()
            };
          } else if (stage === 'document-delivery') {
            updatedReport.status = 'document-delivered';
            // Add document delivery data if needed
          }
          
          return updatedReport;
        }
        return report;
      })
    );
    
    toast({
      title: "Laporan berhasil diperbarui",
      description: stage === 'unloading' 
        ? "Tahap bongkar telah dilaporkan. Lanjutkan ke pengiriman dokumen."
        : "Laporan pengiriman telah diselesaikan sepenuhnya.",
    });
  };

  const getReportByOrderId = (orderId: string) => {
    return deliveryReports.find(report => report.orderId === orderId);
  };

  return {
    deliveryReports,
    handleCreateDeliveryReport,
    handleUpdateDeliveryReport,
    getReportByOrderId
  };
};
