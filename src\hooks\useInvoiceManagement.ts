
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Invoice, InvoiceFormData } from '@/types/invoice';
import { mockInvoices } from '@/data/mockInvoices';

export const useInvoiceManagement = () => {
  const [invoices, setInvoices] = useState<Invoice[]>(mockInvoices);
  const { toast } = useToast();

  const handleCreateInvoice = (invoiceData: InvoiceFormData) => {
    const subtotal = invoiceData.items.reduce((sum, item) => sum + item.total, 0);
    const tax = subtotal * 0.1; // 10% tax
    const total = subtotal + tax;

    const newInvoice: Invoice = {
      ...invoiceData,
      id: `INV-${String(invoices.length + 1).padStart(3, '0')}`,
      invoiceNumber: `INV-2024-${String(invoices.length + 1).padStart(3, '0')}`,
      issueDate: new Date(),
      status: 'draft',
      subtotal,
      tax,
      total
    };
    
    setInvoices(prev => [newInvoice, ...prev]);
    toast({
      title: "Invoice berhasil dibuat",
      description: `${newInvoice.invoiceNumber} telah dibuat`,
    });
  };

  const handleUpdateInvoice = (invoiceId: string, updatedData: Partial<InvoiceFormData>) => {
    setInvoices(prev =>
      prev.map(invoice =>
        invoice.id === invoiceId
          ? { ...invoice, ...updatedData }
          : invoice
      )
    );
    toast({
      title: "Invoice berhasil diperbarui",
      description: "Informasi invoice telah diperbarui",
    });
  };

  const handleUpdateInvoiceStatus = (invoiceId: string, status: Invoice['status']) => {
    setInvoices(prev =>
      prev.map(invoice =>
        invoice.id === invoiceId
          ? { 
              ...invoice, 
              status,
              paymentDate: status === 'paid' ? new Date() : invoice.paymentDate
            }
          : invoice
      )
    );
    
    const invoice = invoices.find(i => i.id === invoiceId);
    if (invoice) {
      toast({
        title: "Status invoice diperbarui",
        description: `${invoice.invoiceNumber} sekarang ${status}`,
      });
    }
  };

  const handleDeleteInvoice = (invoiceId: string) => {
    const invoice = invoices.find(i => i.id === invoiceId);
    setInvoices(prev => prev.filter(i => i.id !== invoiceId));
    
    if (invoice) {
      toast({
        title: "Invoice dihapus",
        description: `${invoice.invoiceNumber} telah dihapus dari sistem`,
      });
    }
  };

  return {
    invoices,
    handleCreateInvoice,
    handleUpdateInvoice,
    handleUpdateInvoiceStatus,
    handleDeleteInvoice
  };
};
