import React from 'react';
import DriverList from '@/components/Drivers/DriverList';
import DriverForm from '@/components/Drivers/DriverForm';
import DriverDetail from '@/components/Drivers/DriverDetail';
import { Driver } from '@/types/driver';

interface DriverSectionProps {
  showDriverForm: boolean;
  currentView: string;
  selectedDriver: Driver | null;
  user: { name: string; role: 'admin' | 'financial' | 'operational' | 'driver' };
  drivers: Driver[];
  onCreateDriver: (driverData: any) => void;
  onViewDriver: (driver: Driver) => void;
  onEditDriver: (driver: Driver) => void;
  onToggleDriverStatus: (driverId: string) => void;
  onShowDriverForm: () => void;
  onCancelDriverForm: () => void;
  onUpdateDriver: (driverData: any) => void;
  onBackToDriverList: () => void;
}

const DriverSection = ({
  showDriverForm,
  currentView,
  selectedDriver,
  user,
  drivers,
  onCreateDriver,
  onViewDriver,
  onEditDriver,
  onToggleDriverStatus,
  onShowDriverForm,
  onCancelDriverForm,
  onUpdateDriver,
  onBackToDriverList,
}: DriverSectionProps) => {
  // Handle driver form display
  if (showDriverForm) {
    return (
      <div className="max-w-full overflow-auto">
        <DriverForm
          driver={currentView === 'driver-edit' ? selectedDriver : null}
          onSubmit={currentView === 'driver-edit' ? onUpdateDriver : onCreateDriver}
          onCancel={currentView === 'driver-edit' ? onBackToDriverList : onCancelDriverForm}
          isEdit={currentView === 'driver-edit'}
        />
      </div>
    );
  }

  // Handle driver detail view
  if (currentView === 'driver-detail' && selectedDriver) {
    return (
      <div className="max-w-full overflow-auto">
        <DriverDetail
          driver={selectedDriver}
          onBack={onBackToDriverList}
          onEdit={() => onEditDriver(selectedDriver)}
        />
      </div>
    );
  }

  // Default driver list view
  return (
    <div className="max-w-full">
      <div className="space-y-4 sm:space-y-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div className="space-y-1">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Kelola Driver</h1>
            <p className="text-sm sm:text-base text-gray-600">Kelola semua driver dalam sistem</p>
          </div>
          {(user.role === 'admin' || user.role === 'operational') && (
            <button
              onClick={onShowDriverForm}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium text-sm sm:text-base w-full sm:w-auto"
            >
              + Tambah Driver Baru
            </button>
          )}
        </div>
        <DriverList
          drivers={drivers}
          onViewDriver={onViewDriver}
          onEditDriver={onEditDriver}
          onToggleStatus={onToggleDriverStatus}
        />
      </div>
    </div>
  );
};

export default DriverSection;
