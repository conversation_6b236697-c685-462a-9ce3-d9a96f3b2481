
import { useState } from 'react';
import { Customer } from '@/types/customer';

export interface OrderFormData {
  customerName: string;
  customerAddress: string;
  customerPhone: string;
  pickupAddress: string;
  deliveryAddress: string;
  pickupDate: Date | undefined;
  deliveryDate: Date | undefined;
  cargoDescription: string;
  weight: string;
  notes: string;
  priority: string;
}

export const useOrderForm = (initialOrder?: any) => {
  // Inisialisasi selectedCustomer jika initialOrder ada
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    initialOrder ? {
      id: initialOrder.customerId || '',
      companyName: initialOrder.customerName || '',
      address: initialOrder.customerAddress || '',
      phone: initialOrder.customerPhone || '',
      email: initialOrder.customerEmail || ''
    } : null
  );
  
  const [isManualEdit, setIsManualEdit] = useState(initialOrder ? true : false);
  
  const [formData, setFormData] = useState<OrderFormData>({
    customerName: initialOrder?.customerName || '',
    customerAddress: initialOrder?.customerAddress || '',
    customerPhone: initialOrder?.customerPhone || '',
    customerEmail: initialOrder?.customerEmail || '',
    pickupAddress: initialOrder?.pickupAddress || '',
    deliveryAddress: initialOrder?.deliveryAddress || '',
    pickupDate: initialOrder?.pickupDate || undefined,
    deliveryDate: initialOrder?.deliveryDate || undefined,
    cargoDescription: initialOrder?.cargoDescription || '',
    weight: initialOrder?.weight || '',
    notes: initialOrder?.notes || '',
    priority: initialOrder?.priority || 'normal',
    assignedDriver: initialOrder?.assignedDriver || '',
    status: initialOrder?.status || 'pending',
    amount: initialOrder?.amount || 0,
    orderNumber: initialOrder?.orderNumber || ''
  });

  const handleCustomerSelect = (customer: Customer | null) => {
    setSelectedCustomer(customer);
    setIsManualEdit(false);
    
    if (customer) {
      setFormData(prev => ({
        ...prev,
        customerName: customer.companyName,
        customerAddress: customer.address,
        customerPhone: customer.phone
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        customerName: '',
        customerAddress: '',
        customerPhone: ''
      }));
    }
  };

  const handleManualEdit = () => {
    setIsManualEdit(true);
  };

  const updateFormData = (field: keyof OrderFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const isCustomerInfoReadOnly = selectedCustomer && !isManualEdit;

  return {
    selectedCustomer,
    isManualEdit,
    formData,
    handleCustomerSelect,
    handleManualEdit,
    updateFormData,
    isCustomerInfoReadOnly
  };
};
