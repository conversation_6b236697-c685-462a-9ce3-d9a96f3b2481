
import { useState } from 'react';
import { Customer } from '@/types/customer';

export interface OrderFormData {
  customerName: string;
  customerAddress: string;
  customerPhone: string;
  pickupAddress: string;
  deliveryAddress: string;
  pickupDate: Date | undefined;
  deliveryDate: Date | undefined;
  cargoDescription: string;
  weight: string;
  notes: string;
  priority: string;
}

export const useOrderForm = (initialOrder?: any) => {
  // Inisialisasi selectedCustomer jika initialOrder ada
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    initialOrder ? {
      id: initialOrder.customerId || '',
      companyName: initialOrder.customerName || '',
      address: initialOrder.customerAddress || '',
      phone: initialOrder.customerPhone || ''
    } : null
  );
  
  // Jika ini adalah mode edit, kita tidak ingin mode manual edit aktif
  // karena kita ingin menampilkan data yang sudah ada
  const [isManualEdit, setIsManualEdit] = useState(false);
  
  const [formData, setFormData] = useState<OrderFormData>({
    customerName: initialOrder?.customerName || '',
    customerAddress: initialOrder?.customerAddress || '',
    customerPhone: initialOrder?.customerPhone || '',
    pickupAddress: initialOrder?.pickupAddress || '',
    deliveryAddress: initialOrder?.deliveryAddress || '',
    pickupDate: initialOrder?.pickupDate || undefined,
    deliveryDate: initialOrder?.deliveryDate || undefined,
    cargoDescription: initialOrder?.cargoDescription || '',
    weight: initialOrder?.weight || '',
    notes: initialOrder?.notes || '',
    priority: initialOrder?.priority || 'normal'
  });

  const handleCustomerSelect = (customer: Customer | null) => {
    setSelectedCustomer(customer);
    setIsManualEdit(false);
    
    if (customer) {
      setFormData(prev => ({
        ...prev,
        customerName: customer.companyName,
        customerAddress: customer.address,
        customerPhone: customer.phone
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        customerName: '',
        customerAddress: '',
        customerPhone: ''
      }));
    }
  };

  const handleManualEdit = () => {
    setIsManualEdit(true);
  };

  const updateFormData = (field: keyof OrderFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Jika ini adalah mode edit dan ada selectedCustomer, kita ingin
  // kolom-kolom customer bisa diedit
  const isCustomerInfoReadOnly = selectedCustomer && !isManualEdit && !initialOrder;

  return {
    selectedCustomer,
    isManualEdit,
    formData,
    handleCustomerSelect,
    handleManualEdit,
    updateFormData,
    isCustomerInfoReadOnly
  };
};
