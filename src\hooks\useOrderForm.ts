
import { useState } from 'react';
import { Customer } from '@/types/customer';

export interface OrderFormData {
  customerName: string;
  customerAddress: string;
  customerPhone: string;
  pickupAddress: string;
  deliveryAddress: string;
  pickupDate: Date | undefined;
  deliveryDate: Date | undefined;
  cargoDescription: string;
  weight: string;
  notes: string;
  priority: string;
}

export const useOrderForm = (initialOrder?: any) => {
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isManualEdit, setIsManualEdit] = useState(false);
  
  const [formData, setFormData] = useState<OrderFormData>({
    customerName: initialOrder?.customerName || '',
    customerAddress: initialOrder?.customerAddress || '',
    customerPhone: initialOrder?.customerPhone || '',
    pickupAddress: initialOrder?.pickupAddress || '',
    deliveryAddress: initialOrder?.deliveryAddress || '',
    pickupDate: initialOrder?.pickupDate || undefined,
    deliveryDate: initialOrder?.deliveryDate || undefined,
    cargoDescription: initialOrder?.cargoDescription || '',
    weight: initialOrder?.weight || '',
    notes: initialOrder?.notes || '',
    priority: initialOrder?.priority || 'normal'
  });

  const handleCustomerSelect = (customer: Customer | null) => {
    setSelectedCustomer(customer);
    setIsManualEdit(false);
    
    if (customer) {
      setFormData(prev => ({
        ...prev,
        customerName: customer.companyName,
        customerAddress: customer.address,
        customerPhone: customer.phone
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        customerName: '',
        customerAddress: '',
        customerPhone: ''
      }));
    }
  };

  const handleManualEdit = () => {
    setIsManualEdit(true);
  };

  const updateFormData = (field: keyof OrderFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const isCustomerInfoReadOnly = selectedCustomer && !isManualEdit;

  return {
    selectedCustomer,
    isManualEdit,
    formData,
    handleCustomerSelect,
    handleManualEdit,
    updateFormData,
    isCustomerInfoReadOnly
  };
};
