
export interface VendorCompany {
  id: string;
  name: string;
  address: string;
  phone: string;
  email: string;
  contactPerson: string;
  contractStart: Date;
  contractEnd: Date;
  serviceAreas: string[];
  vehicleTypes: string[];
  rating: number;
  totalOrders: number;
  status: 'active' | 'inactive' | 'suspended';
}

export const mockVendorCompanies: VendorCompany[] = [
  {
    id: 'VENDOR-A',
    name: 'PT. Logistik Surabaya Mandiri',
    address: 'Jl. Ahmad Yani No. 500, Surabaya',
    phone: '+62319876543',
    email: '<EMAIL>',
    contactPerson: '<PERSON><PERSON>',
    contractStart: new Date('2023-01-01'),
    contractEnd: new Date('2025-12-31'),
    serviceAreas: ['Surabaya', 'Malang', 'Kediri', 'Jember'],
    vehicleTypes: ['Truk Kecil', 'Van', 'Pick Up'],
    rating: 4.5,
    totalOrders: 156,
    status: 'active'
  },
  {
    id: 'VENDOR-B',
    name: 'CV. Ekspedisi Bandung Jaya',
    address: 'Jl. Diponegoro No. 800, Bandung',
    phone: '+62227654321',
    email: '<EMAIL>',
    contactPerson: 'Susi Rahayu',
    contractStart: new Date('2022-06-01'),
    contractEnd: new Date('2025-05-31'),
    serviceAreas: ['Bandung', 'Cirebon', 'Garut', 'Tasikmalaya'],
    vehicleTypes: ['Truk Sedang', 'Truk Besar', 'Container'],
    rating: 4.8,
    totalOrders: 298,
    status: 'active'
  },
  {
    id: 'VENDOR-C',
    name: 'PT. Transportasi Semarang Utama',
    address: 'Jl. Hayam Wuruk No. 950, Semarang',
    phone: '+62248765432',
    email: '<EMAIL>',
    contactPerson: 'Ahmad Fauzi',
    contractStart: new Date('2023-09-01'),
    contractEnd: new Date('2026-08-31'),
    serviceAreas: ['Semarang', 'Solo', 'Yogyakarta', 'Purwokerto'],
    vehicleTypes: ['Van', 'Mobil Box', 'Truk Kecil'],
    rating: 4.4,
    totalOrders: 87,
    status: 'active'
  }
];
