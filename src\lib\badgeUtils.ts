import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Vehicle } from '@/types/vehicle';

export const getVehicleTypeBadge = (type: string) => {
  switch (type) {
    case 'Truk Kecil':
      return <Badge className="bg-amber-100 text-amber-800">Truk Kecil</Badge>;
    case 'Truk Sedang':
      return <Badge className="bg-orange-100 text-orange-800">Truk Sedang</Badge>;
    case 'Truk Besar':
      return <Badge className="bg-red-100 text-red-800">Truk Besar</Badge>;
    case 'Van':
      return <Badge className="bg-green-100 text-green-800">Van</Badge>;
    case 'Pick Up':
      return <Badge className="bg-blue-100 text-blue-800">Pick Up</Badge>;
    case 'Mobil Box':
      return <Badge className="bg-indigo-100 text-indigo-800">Mobil Box</Badge>;
    case 'Container':
      return <Badge className="bg-purple-100 text-purple-800">Container</Badge>;
    default:
      return <Badge variant="outline">{type}</Badge>;
  }
};

export const getVehicleSourceBadge = (source: string) => {
  switch (source) {
    case 'internal':
      return <Badge className="bg-blue-100 text-blue-800">Internal</Badge>;
    case 'vendor':
      return <Badge className="bg-purple-100 text-purple-800">Vendor</Badge>;
    default:
      return <Badge variant="outline">Unknown</Badge>;
  }
};

export const getStatusColor = (status: string) => {
  switch (status) {
    case 'available': return 'bg-green-500';
    case 'in-transit': return 'bg-blue-500';
    case 'maintenance': return 'bg-yellow-500';
    case 'out-of-service': return 'bg-red-500';
    default: return 'bg-gray-500';
  }
};

export const getStatusText = (status: string) => {
  switch (status) {
    case 'available': return 'Tersedia';
    case 'in-transit': return 'Dalam Perjalanan';
    case 'maintenance': return 'Maintenance';
    case 'out-of-service': return 'Tidak Beroperasi';
    default: return status;
  }
};
