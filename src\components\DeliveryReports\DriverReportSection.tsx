import React from 'react';
import InternalLoadingForm from './InternalLoadingForm';
import VendorLoadingForm from './VendorLoadingForm';
import VendorUnloadingForm from './VendorUnloadingForm';
import { DeliveryReportFormData, DeliveryReport } from '@/types/deliveryReport';
import { Button } from '@/components/ui/button';

interface DriverReportSectionProps {
  currentView: string;
  driverType: 'internal' | 'vendor';
  selectedOrder: any;
  selectedReport: DeliveryReport | null;
  onCreateReport: (data: DeliveryReportFormData) => void;
  onUpdateReport: (reportId: string, data: DeliveryReportFormData, stage: 'unloading' | 'document-delivery') => void;
  onCancel: () => void;
  onBackToList: () => void;
}

const DriverReportSection = ({
  currentView,
  driverType,
  selectedOrder,
  selectedReport,
  onCreateReport,
  onUpdateReport,
  onCancel,
  onBackToList
}: DriverReportSectionProps) => {
  if (currentView === 'loading-report') {
    if (driverType === 'internal') {
      return (
        <InternalLoadingForm
          orderId={selectedOrder?.id || ''}
          orderNumber={selectedOrder?.orderNumber || ''}
          onSubmit={onCreateReport}
          onCancel={onCancel}
        />
      );
    } else {
      return (
        <VendorLoadingForm
          orderId={selectedOrder?.id || ''}
          orderNumber={selectedOrder?.orderNumber || ''}
          onSubmit={onCreateReport}
          onCancel={onCancel}
        />
      );
    }
  }

  if (currentView === 'unloading-report' && driverType === 'vendor' && selectedReport) {
    return (
      <VendorUnloadingForm
        reportId={selectedReport.id}
        orderNumber={selectedReport.orderNumber}
        onSubmit={(data) => onUpdateReport(selectedReport.id, data, 'unloading')}
        onCancel={onBackToList}
      />
    );
  }

  // Default view - show a message or redirect
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <h2 className="text-2xl font-bold mb-4">Tidak ada form yang aktif</h2>
      <p className="text-gray-600 mb-6">Silakan pilih menu untuk melaporkan muat atau bongkar</p>
      <Button onClick={onBackToList}>Kembali ke Dashboard</Button>
    </div>
  );
};

export default DriverReportSection;