
import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileText, Truck, DollarSign, Users } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isUp: boolean;
  };
}

const StatCard = ({ title, value, icon, trend }: StatCardProps) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      {icon}
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      {trend && (
        <p className={`text-xs ${trend.isUp ? 'text-green-600' : 'text-red-600'}`}>
          {trend.isUp ? '↗' : '↘'} {Math.abs(trend.value)}% dari bulan lalu
        </p>
      )}
    </CardContent>
  </Card>
);

interface DashboardStatsProps {
  userRole: 'admin' | 'financial' | 'operational' | 'driver';
}

const DashboardStats = ({ userRole }: DashboardStatsProps) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatsForRole = () => {
    switch (userRole) {
      case 'admin':
        return [
          {
            title: 'Total Pesanan Bulan Ini',
            value: '147',
            icon: <FileText className="h-4 w-4 text-blue-600" />,
            trend: { value: 12, isUp: true }
          },
          {
            title: 'Driver Aktif',
            value: '23',
            icon: <Users className="h-4 w-4 text-green-600" />,
            trend: { value: 5, isUp: true }
          },
          {
            title: 'Kendaraan Operasional',
            value: '18',
            icon: <Truck className="h-4 w-4 text-yellow-600" />,
            trend: { value: 2, isUp: false }
          },
          {
            title: 'Pendapatan Bulan Ini',
            value: formatCurrency(285750000),
            icon: <DollarSign className="h-4 w-4 text-purple-600" />,
            trend: { value: 8, isUp: true }
          }
        ];

      case 'financial':
        return [
          {
            title: 'Invoice Tertunda',
            value: '12',
            icon: <FileText className="h-4 w-4 text-orange-600" />
          },
          {
            title: 'Pendapatan Bulan Ini',
            value: formatCurrency(285750000),
            icon: <DollarSign className="h-4 w-4 text-green-600" />,
            trend: { value: 8, isUp: true }
          },
          {
            title: 'Pembayaran Tertunda',
            value: formatCurrency(45200000),
            icon: <DollarSign className="h-4 w-4 text-red-600" />
          },
          {
            title: 'PPh 2% Bulan Ini',
            value: formatCurrency(5715000),
            icon: <DollarSign className="h-4 w-4 text-blue-600" />
          }
        ];

      case 'operational':
        return [
          {
            title: 'Pesanan Hari Ini',
            value: '8',
            icon: <FileText className="h-4 w-4 text-blue-600" />
          },
          {
            title: 'Driver Tersedia',
            value: '15',
            icon: <Users className="h-4 w-4 text-green-600" />
          },
          {
            title: 'Kendaraan Siap',
            value: '12',
            icon: <Truck className="h-4 w-4 text-yellow-600" />
          },
          {
            title: 'Pesanan Selesai',
            value: '134',
            icon: <FileText className="h-4 w-4 text-purple-600" />,
            trend: { value: 6, isUp: true }
          }
        ];

      case 'driver':
        return [
          {
            title: 'Pesanan Hari Ini',
            value: '3',
            icon: <FileText className="h-4 w-4 text-blue-600" />
          },
          {
            title: 'Pesanan Selesai',
            value: '2',
            icon: <FileText className="h-4 w-4 text-green-600" />
          },
          {
            title: 'Jarak Tempuh Hari Ini',
            value: '245 km',
            icon: <Truck className="h-4 w-4 text-yellow-600" />
          },
          {
            title: 'Pendapatan Bulan Ini',
            value: formatCurrency(12500000),
            icon: <DollarSign className="h-4 w-4 text-purple-600" />
          }
        ];

      default:
        return [];
    }
  };

  const stats = getStatsForRole();

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <StatCard key={index} {...stat} />
      ))}
    </div>
  );
};

export default DashboardStats;
