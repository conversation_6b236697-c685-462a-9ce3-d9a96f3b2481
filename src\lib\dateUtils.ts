// Standard date formatting functions for Indonesian locale

/**
 * Format date to long format (e.g., "15 Januari 2024")
 */
export const formatDate = (date: Date | string) => {
  const dateObj = date instanceof Date ? date : new Date(date);
  return new Intl.DateTimeFormat('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(dateObj);
};

/**
 * Format date to short format (e.g., "15 Jan 2024")
 */
export const formatDateShort = (date: Date | string) => {
  const dateObj = date instanceof Date ? date : new Date(date);
  return new Intl.DateTimeFormat('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(dateObj);
};

/**
 * Format date to simple format (e.g., "15/01/2024")
 */
export const formatDateSimple = (date: Date | string) => {
  const dateObj = date instanceof Date ? date : new Date(date);
  return dateObj.toLocaleDateString('id-ID');
};

/**
 * Format date to ISO string for input fields (e.g., "2024-01-15")
 */
export const formatDateToISO = (date: Date | string) => {
  if (!date) return '';
  const dateObj = date instanceof Date ? date : new Date(date);
  return dateObj.toISOString().split('T')[0];
};

/**
 * Format date with day name (e.g., "Senin, 15 Januari 2024")
 */
export const formatDateWithDay = (date: Date | string) => {
  const dateObj = date instanceof Date ? date : new Date(date);
  return new Intl.DateTimeFormat('id-ID', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(dateObj);
};

/**
 * Format date for display in lists (e.g., "15/01/24")
 */
export const formatDateCompact = (date: Date | string) => {
  const dateObj = date instanceof Date ? date : new Date(date);
  return new Intl.DateTimeFormat('id-ID', {
    year: '2-digit',
    month: '2-digit',
    day: '2-digit'
  }).format(dateObj);
};

/**
 * Calculate days difference between two dates
 */
export const getDaysDifference = (date1: Date | string, date2: Date | string) => {
  const dateObj1 = date1 instanceof Date ? date1 : new Date(date1);
  const dateObj2 = date2 instanceof Date ? date2 : new Date(date2);
  const diffTime = dateObj2.getTime() - dateObj1.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

/**
 * Check if a date is expired (before today)
 */
export const isDateExpired = (date: Date | string) => {
  const dateObj = date instanceof Date ? date : new Date(date);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  dateObj.setHours(0, 0, 0, 0);
  return dateObj < today;
};

/**
 * Check if a date is expiring soon (within specified days)
 */
export const isDateExpiringSoon = (date: Date | string, daysThreshold: number = 30) => {
  const dateObj = date instanceof Date ? date : new Date(date);
  const today = new Date();
  const diffDays = getDaysDifference(today, dateObj);
  return diffDays <= daysThreshold && diffDays > 0;
};