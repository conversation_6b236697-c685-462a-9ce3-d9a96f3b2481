
export interface ReportStats {
  totalOrders: number;
  completedOrders: number;
  pendingOrders: number;
  totalRevenue: number;
  totalDrivers: number;
  activeDrivers: number;
  totalVehicles: number;
  availableVehicles: number;
}

export interface ReportFilters {
  startDate: Date;
  endDate: Date;
  driverId?: string;
  vehicleId?: string;
  status?: string;
}

export interface OrderReportData {
  date: string;
  orders: number;
  revenue: number;
  completed: number;
  pending: number;
}

export interface DriverReportData {
  driverId: string;
  name: string;
  totalOrders: number;
  completedOrders: number;
  rating: number;
  revenue: number;
}

export interface VehicleReportData {
  vehicleId: string;
  plateNumber: string;
  type: string;
  totalOrders: number;
  utilizationRate: number;
  maintenanceCost: number;
}
