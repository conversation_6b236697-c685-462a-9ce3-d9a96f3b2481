
import { Invoice } from '@/types/invoice';

export const mockInvoices: Invoice[] = [
  {
    id: 'INV-001',
    invoiceNumber: 'INV-2024-001',
    orderId: 'ORD-001',
    customerName: 'PT. <PERSON><PERSON>',
    customerEmail: '<EMAIL>',
    customerAddress: 'Jl. Sudirman No. 123, Jakarta',
    issueDate: new Date('2024-12-01'),
    dueDate: new Date('2024-12-31'),
    status: 'sent',
    items: [
      {
        id: 'ITEM-001',
        description: 'Pengiriman barang Jakarta-Surabaya',
        quantity: 1,
        unitPrice: 2500000,
        total: 2500000
      },
      {
        id: 'ITEM-002',
        description: '<PERSON><PERSON><PERSON> bongkar muat',
        quantity: 2,
        unitPrice: 150000,
        total: 300000
      }
    ],
    subtotal: 2800000,
    tax: 280000,
    total: 3080000,
    notes: 'Pembayaran maksimal 30 hari setelah invoice'
  },
  {
    id: 'INV-002',
    invoiceNumber: 'INV-2024-002',
    orderId: 'ORD-002',
    customerName: 'CV. Sejahtera',
    customerEmail: '<EMAIL>',
    customerAddress: 'Jl. Gatot Subroto No. 456, Bandung',
    issueDate: new Date('2024-12-05'),
    dueDate: new Date('2024-12-20'),
    status: 'paid',
    items: [
      {
        id: 'ITEM-003',
        description: 'Pengiriman elektronik Jakarta-Bandung',
        quantity: 1,
        unitPrice: 1800000,
        total: 1800000
      }
    ],
    subtotal: 1800000,
    tax: 180000,
    total: 1980000,
    paymentDate: new Date('2024-12-15'),
    notes: 'Terima kasih atas pembayaran tepat waktu'
  }
];
