
import React, { useState } from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Customer } from '@/types/customer';

interface CustomerComboBoxProps {
  customers: Customer[];
  value?: string;
  onSelect: (customer: Customer | null) => void;
  placeholder?: string;
}

const CustomerComboBox = ({ 
  customers, 
  value, 
  onSelect, 
  placeholder = "Pilih perusahaan..." 
}: CustomerComboBoxProps) => {
  const [open, setOpen] = useState(false);

  const selectedCustomer = customers.find(customer => customer.id === value);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {selectedCustomer ? selectedCustomer.companyName : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput placeholder="Cari perusahaan..." />
          <CommandList>
            <CommandEmpty>Tidak ada perusahaan ditemukan.</CommandEmpty>
            <CommandGroup>
              {customers.map((customer) => (
                <CommandItem
                  key={customer.id}
                  value={customer.companyName}
                  onSelect={() => {
                    if (value === customer.id) {
                      onSelect(null);
                    } else {
                      onSelect(customer);
                    }
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === customer.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <div className="flex flex-col">
                    <span className="font-medium">{customer.companyName}</span>
                    <span className="text-xs text-muted-foreground truncate">
                      {customer.address}
                    </span>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default CustomerComboBox;
