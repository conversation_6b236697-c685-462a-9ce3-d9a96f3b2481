
import { Vehicle } from '@/types/vehicle';

export const mockVehicles: Vehicle[] = [
  {
    id: 'VEH-001',
    plateNumber: 'B 1234 ABC',
    type: 'Truk Kecil',
    vehicleSource: 'internal',
    brand: 'Mitsubishi',
    model: 'Colt Diesel',
    year: 2020,
    capacity: 3000,
    status: 'available',
    condition: 'excellent',
    fuelType: 'Solar',
    lastMaintenance: new Date('2023-11-15'),
    nextMaintenance: new Date('2024-02-15'),
    mileage: 45000,
    assignedDriverId: 'DRV-001',
    registrationExpiry: new Date('2025-08-20'),
    insuranceExpiry: new Date('2025-06-30'),
    notes: 'Kendaraan dalam kondisi prima'
  },
  {
    id: 'VEH-002',
    plateNumber: 'B 5678 DEF',
    type: 'Truk Sedang',
    vehicleSource: 'vendor',
    brand: 'Hino',
    model: 'Dutro',
    year: 2019,
    capacity: 5000,
    status: 'in-transit',
    condition: 'good',
    fuelType: 'Solar',
    lastMaintenance: new Date('2023-10-05'),
    nextMaintenance: new Date('2024-01-05'),
    mileage: 62000,
    assignedDriverId: 'DRV-002',
    registrationExpiry: new Date('2024-12-15'),
    insuranceExpiry: new Date('2024-11-30'),
    notes: 'Perlu pengecekan rem'
  },
  {
    id: 'VEH-003',
    plateNumber: 'B 9012 GHI',
    type: 'Van',
    vehicleSource: 'internal',
    brand: 'Daihatsu',
    model: 'Gran Max',
    year: 2021,
    capacity: 1000,
    status: 'available',
    condition: 'excellent',
    fuelType: 'Bensin',
    lastMaintenance: new Date('2023-12-01'),
    nextMaintenance: new Date('2024-03-01'),
    mileage: 28000,
    registrationExpiry: new Date('2025-03-10'),
    insuranceExpiry: new Date('2025-02-28'),
    notes: ''
  },
  {
    id: 'VEH-004',
    plateNumber: 'B 3456 JKL',
    type: 'Truk Besar',
    vehicleSource: 'vendor',
    brand: 'Isuzu',
    model: 'Giga',
    year: 2018,
    capacity: 8000,
    status: 'maintenance',
    condition: 'fair',
    fuelType: 'Solar',
    lastMaintenance: new Date('2023-11-20'),
    nextMaintenance: new Date('2024-02-20'),
    mileage: 85000,
    registrationExpiry: new Date('2024-09-05'),
    insuranceExpiry: new Date('2024-08-15'),
    notes: 'Sedang dalam perbaikan mesin'
  },
  {
    id: 'VEH-005',
    plateNumber: 'B 7890 MNO',
    type: 'Pick Up',
    vehicleSource: 'internal',
    brand: 'Toyota',
    model: 'Hilux',
    year: 2022,
    capacity: 1500,
    status: 'available',
    condition: 'excellent',
    fuelType: 'Solar',
    lastMaintenance: new Date('2023-12-10'),
    nextMaintenance: new Date('2024-03-10'),
    mileage: 15000,
    registrationExpiry: new Date('2025-10-25'),
    insuranceExpiry: new Date('2025-09-30'),
    notes: ''
  }
];
