
import React, { useState } from 'react';
import { Camera, Upload, X, Video, Image } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface PhotoUploadComponentProps {
  label: string;
  maxFiles?: number;
  acceptVideo?: boolean;
  files: File[];
  onFilesChange: (files: File[]) => void;
  required?: boolean;
}

const PhotoUploadComponent = ({ 
  label, 
  maxFiles = 5, 
  acceptVideo = false, 
  files, 
  onFilesChange,
  required = false 
}: PhotoUploadComponentProps) => {
  const [dragOver, setDragOver] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || []);
    const newFiles = [...files, ...selectedFiles].slice(0, maxFiles);
    onFilesChange(newFiles);
  };

  const removeFile = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index);
    onFilesChange(newFiles);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
    const droppedFiles = Array.from(event.dataTransfer.files);
    const newFiles = [...files, ...droppedFiles].slice(0, maxFiles);
    onFilesChange(newFiles);
  };

  const acceptTypes = acceptVideo 
    ? "image/*,video/*" 
    : "image/*";

  return (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-gray-700">
        {label} {required && <span className="text-red-500">*</span>}
        {maxFiles > 1 && <span className="text-gray-500 text-xs ml-1">(Max {maxFiles} files)</span>}
      </label>
      
      <div
        className={`border-2 border-dashed rounded-lg p-4 transition-colors ${
          dragOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300'
        }`}
        onDragOver={(e) => { e.preventDefault(); setDragOver(true); }}
        onDragLeave={() => setDragOver(false)}
        onDrop={handleDrop}
      >
        <div className="text-center">
          <div className="flex justify-center space-x-2 mb-2">
            <Camera className="h-8 w-8 text-gray-400" />
            {acceptVideo && <Video className="h-8 w-8 text-gray-400" />}
          </div>
          <p className="text-sm text-gray-600 mb-2">
            Drag & drop files here or click to browse
          </p>
          <input
            type="file"
            multiple={maxFiles > 1}
            accept={acceptTypes}
            onChange={handleFileChange}
            className="hidden"
            id={`file-input-${label.replace(/\s+/g, '-')}`}
          />
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => document.getElementById(`file-input-${label.replace(/\s+/g, '-')}`)?.click()}
          >
            <Upload className="h-4 w-4 mr-2" />
            Choose Files
          </Button>
        </div>
      </div>

      {files.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
          {files.map((file, index) => (
            <Card key={index} className="relative">
              <CardContent className="p-2">
                <div className="aspect-square bg-gray-100 rounded flex items-center justify-center relative">
                  {file.type.startsWith('image/') ? (
                    <div className="flex flex-col items-center">
                      <Image className="h-8 w-8 text-gray-500" />
                      <span className="text-xs text-gray-500 mt-1 truncate w-full text-center">
                        {file.name}
                      </span>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <Video className="h-8 w-8 text-gray-500" />
                      <span className="text-xs text-gray-500 mt-1 truncate w-full text-center">
                        {file.name}
                      </span>
                    </div>
                  )}
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute -top-1 -right-1 h-6 w-6 rounded-full p-0"
                    onClick={() => removeFile(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default PhotoUploadComponent;
