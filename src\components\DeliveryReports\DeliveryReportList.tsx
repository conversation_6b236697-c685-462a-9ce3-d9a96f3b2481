import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DeliveryReport } from '@/types/deliveryReport';
import { Order } from '@/types/order';
import { format } from 'date-fns';
import { Clock, CheckCircle, Package, FileText, Truck } from 'lucide-react';

interface DeliveryReportListProps {
  reports: DeliveryReport[];
  orders: Order[];
  driverType: 'internal' | 'vendor';
  onCreateReport: (orderId: string) => void;
  onContinueReport: (report: DeliveryReport, stage: 'unloading' | 'document-delivery') => void;
}

const DeliveryReportList = ({ 
  reports, 
  orders, 
  driverType, 
  onCreateReport, 
  onContinueReport 
}: DeliveryReportListProps) => {
  
  const getStatusBadge = (status: DeliveryReport['status']) => {
    switch (status) {
      case 'draft':
        return <Badge variant="secondary">Draft</Badge>;
      case 'loading-reported':
        return <Badge className="bg-yellow-500">Tahap Muat Selesai</Badge>;
      case 'unloading-reported':
        return <Badge className="bg-blue-500">Tahap Bongkar Selesai</Badge>;
      case 'document-delivered':
        return <Badge className="bg-purple-500">Dokumen Terkirim</Badge>;
      case 'completed':
        return <Badge className="bg-green-500">Selesai</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getProgressSteps = (status: DeliveryReport['status']) => {
    if (driverType === 'internal') {
      return (
        <div className="flex items-center space-x-2 text-xs">
          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
            status === 'completed' ? 'bg-green-500 text-white' : 'bg-gray-300'
          }`}>
            {status === 'completed' ? '✓' : '1'}
          </div>
          <span className={status === 'completed' ? 'text-green-600' : 'text-gray-500'}>
            Laporan Lengkap
          </span>
        </div>
      );
    }

    return (
      <div className="flex items-center space-x-1 text-xs">
        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
          ['loading-reported', 'unloading-reported', 'document-delivered', 'completed'].includes(status)
            ? 'bg-green-500 text-white' : 'bg-gray-300'
        }`}>
          {['loading-reported', 'unloading-reported', 'document-delivered', 'completed'].includes(status) ? '✓' : '1'}
        </div>
        <span className={['loading-reported', 'unloading-reported', 'document-delivered', 'completed'].includes(status) ? 'text-green-600 text-xs' : 'text-gray-500 text-xs'}>Muat</span>
        
        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
          ['unloading-reported', 'document-delivered', 'completed'].includes(status)
            ? 'bg-green-500 text-white' : status === 'loading-reported' ? 'bg-blue-500 text-white' : 'bg-gray-300'
        }`}>
          {['unloading-reported', 'document-delivered', 'completed'].includes(status) ? '✓' : '2'}
        </div>
        <span className={['unloading-reported', 'document-delivered', 'completed'].includes(status) ? 'text-green-600 text-xs' : 'text-gray-500 text-xs'}>Bongkar</span>
        
        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
          ['document-delivered', 'completed'].includes(status)
            ? 'bg-green-500 text-white' : status === 'unloading-reported' ? 'bg-blue-500 text-white' : 'bg-gray-300'
        }`}>
          {['document-delivered', 'completed'].includes(status) ? '✓' : '3'}
        </div>
        <span className={['document-delivered', 'completed'].includes(status) ? 'text-green-600 text-xs' : 'text-gray-500 text-xs'}>Dokumen</span>
      </div>
    );
  };

  const getAvailableOrders = () => {
    const reportedOrderIds = reports.map(r => r.orderId);
    return orders.filter(order => 
      order.status === 'assigned' && 
      !reportedOrderIds.includes(order.orderNumber)
    );
  };

  const getNextAction = (report: DeliveryReport) => {
    if (driverType === 'internal') {
      return null;
    }

    switch (report.status) {
      case 'loading-reported':
        return (
          <Button 
            size="sm" 
            onClick={() => onContinueReport(report, 'unloading')}
            className="bg-blue-500 hover:bg-blue-600"
          >
            Laporkan Bongkar
          </Button>
        );
      case 'unloading-reported':
        return (
          <Button 
            size="sm" 
            onClick={() => onContinueReport(report, 'document-delivery')}
            className="bg-purple-500 hover:bg-purple-600"
          >
            Kirim Dokumen
          </Button>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-semibold">Laporan Pengiriman</h2>
          <p className="text-sm text-gray-600">
            {driverType === 'internal' ? 'Driver Internal' : 'Driver Vendor (3 Tahap)'}
          </p>
        </div>
      </div>

      {/* Available Orders */}
      {getAvailableOrders().length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <Package className="h-5 w-5 mr-2" />
              Pesanan Tersedia untuk Dilaporkan
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {getAvailableOrders().map((order) => (
                <div key={order.orderNumber} className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{order.orderNumber}</p>
                    <p className="text-sm text-gray-600">{order.customerName}</p>
                    <p className="text-xs text-gray-500">
                      Pickup: {format(new Date(order.pickupDate), 'dd/MM/yyyy')}
                    </p>
                  </div>
                  <Button size="sm" onClick={() => onCreateReport(order.orderNumber)}>
                    Buat Laporan
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Existing Reports */}
      <div className="space-y-3">
        {reports.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mb-4" />
              <p className="text-gray-500">Belum ada laporan pengiriman</p>
            </CardContent>
          </Card>
        ) : (
          reports.map((report) => {
            const order = orders.find(o => o.orderNumber === report.orderId);
            return (
              <Card key={report.id}>
                <CardContent className="p-4">
                  <div className="flex flex-col sm:flex-row sm:justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium">{report.orderNumber}</h3>
                        {getStatusBadge(report.status)}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{order?.customerName}</p>
                      <div className="mb-3">
                        {getProgressSteps(report.status)}
                      </div>
                      <p className="text-xs text-gray-500">
                        Dibuat: {format(new Date(report.createdAt), 'dd/MM/yyyy HH:mm')}
                      </p>
                    </div>
                    <div className="flex flex-col items-end gap-2">
                      {getNextAction(report)}
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
    </div>
  );
};

export default DeliveryReportList;
