
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Order } from '@/types/order';

// Mock orders data with vendor driver assignments
const mockOrders: Order[] = [
  {
    orderNumber: 'ORD-2024-001',
    customerName: 'PT. Elektronik Jaya',
    pickupAddress: 'Jl. Industri No. 123, Bekasi',
    deliveryAddress: 'Jl. Raya Jakarta No. 456, Jakarta Pusat',
    pickupDate: new Date('2024-12-10'),
    deliveryDate: new Date('2024-12-11'),
    status: 'completed',
    priority: 'high',
    assignedDriver: 'Budi Santoso',
    amount: 2500000,
    cargoDescription: 'Komponen elektronik dan spare parts',
    assignedAt: new Date('2024-12-09'),
    driverNotes: 'Pengiriman selesai tepat waktu'
  },
  {
    orderNumber: 'ORD-2024-002',
    customerName: 'CV. Mebel Indah',
    pickupAddress: 'Jl. Furniture No. 789, Tangerang',
    deliveryAddress: 'Jl. <PERSON><PERSON><PERSON> No. 321, Jakarta Selatan',
    pickupDate: new Date('2024-12-12'),
    deliveryDate: new Date('2024-12-13'),
    status: 'in-progress',
    priority: 'normal',
    assignedDriver: 'Andi Wijaya',
    amount: 1800000,
    cargoDescription: 'Furniture set untuk kantor',
    assignedAt: new Date('2024-12-11'),
    driverNotes: 'Sedang dalam perjalanan, estimasi tiba sore'
  },
  {
    orderNumber: 'ORD-2024-003',
    customerName: 'PT. Tekstil Prima',
    pickupAddress: 'Kawasan Industri MM2100, Bekasi',
    deliveryAddress: 'Jl. Hayam Wuruk No. 100, Jakarta Barat',
    pickupDate: new Date('2024-12-15'),
    deliveryDate: new Date('2024-12-16'),
    status: 'assigned',
    priority: 'urgent',
    assignedDriver: 'Joko Susilo',
    amount: 3200000,
    cargoDescription: 'Bahan tekstil premium',
    assignedAt: new Date('2024-12-14'),
    driverNotes: 'Driver vendor siap berangkat besok pagi'
  },
  {
    orderNumber: 'ORD-2024-004',
    customerName: 'PT. Farmasi Sehat',
    pickupAddress: 'Jl. Raya Bogor No. 88, Depok',
    deliveryAddress: 'Jl. Ahmad Yani No. 200, Surabaya',
    pickupDate: new Date('2024-12-16'),
    deliveryDate: new Date('2024-12-17'),
    status: 'assigned',
    priority: 'high',
    assignedDriver: 'Agus Prasetyo',
    amount: 4500000,
    cargoDescription: 'Obat-obatan dan alat medis',
    assignedAt: new Date('2024-12-15'),
    driverNotes: 'Vendor driver B siap untuk pengiriman jarak jauh'
  },
  {
    orderNumber: 'ORD-2024-005',
    customerName: 'CV. Makanan Lezat',
    pickupAddress: 'Jl. Veteran No. 45, Semarang',
    deliveryAddress: 'Jl. Malioboro No. 99, Yogyakarta',
    pickupDate: new Date('2024-12-17'),
    deliveryDate: new Date('2024-12-17'),
    status: 'assigned',
    priority: 'normal',
    assignedDriver: 'Bambang Utomo',
    amount: 1200000,
    cargoDescription: 'Produk makanan kemasan',
    assignedAt: new Date('2024-12-16'),
    driverNotes: 'Pengiriman same day service'
  },
  {
    orderNumber: 'ORD-2024-006',
    customerName: 'PT. Konstruksi Maju',
    pickupAddress: 'Jl. Industri Cikarang No. 77, Bekasi',
    deliveryAddress: 'Jl. Panglima Sudirman No. 150, Bandung',
    pickupDate: new Date('2024-12-18'),
    deliveryDate: new Date('2024-12-19'),
    status: 'pending',
    priority: 'normal',
    amount: 2800000,
    cargoDescription: 'Material bangunan dan tools'
  },
  {
    orderNumber: 'ORD-2024-007',
    customerName: 'PT. Technology Innovation',
    pickupAddress: 'Jl. TB Simatupang No. 1, Jakarta Selatan',
    deliveryAddress: 'Jl. Asia Afrika No. 50, Bandung',
    pickupDate: new Date('2024-12-19'),
    deliveryDate: new Date('2024-12-20'),
    status: 'pending',
    priority: 'urgent',
    amount: 5200000,
    cargoDescription: 'Server dan perangkat IT'
  },
  {
    orderNumber: 'ORD-2024-008',
    customerName: 'CV. Fashion Trendy',
    pickupAddress: 'Jl. Mangga Dua No. 33, Jakarta Utara',
    deliveryAddress: 'Jl. Malioboro No. 125, Yogyakarta',
    pickupDate: new Date('2024-12-20'),
    deliveryDate: new Date('2024-12-21'),
    status: 'assigned',
    priority: 'low',
    assignedDriver: 'Rudi Hartono',
    amount: 1500000,
    cargoDescription: 'Pakaian dan aksesoris fashion',
    assignedAt: new Date('2024-12-19'),
    driverNotes: 'Vendor driver siap dengan truk besar'
  }
];

export const useOrderManagement = () => {
  const [orders, setOrders] = useState<Order[]>(mockOrders);
  const { toast } = useToast();

  const handleCreateOrder = (orderData: any) => {
    const newOrder: Order = {
      ...orderData,
      orderNumber: `ORD-2024-${String(orders.length + 1).padStart(3, '0')}`
    };
    setOrders(prev => [newOrder, ...prev]);
    toast({
      title: "Pesanan berhasil dibuat",
      description: `Pesanan ${newOrder.orderNumber} telah ditambahkan`,
    });
  };

  const handleViewOrder = (order: Order, onNavigate?: () => void) => {
    if (onNavigate) {
      onNavigate();
    } else {
      console.log('Viewing order:', order.orderNumber);
    }
  };

  const handleEditOrder = (order: Order, updatedData?: any, onNavigate?: () => void) => {
    if (updatedData) {
      // Actually update the order
      setOrders(prev => 
        prev.map(o => 
          o.orderNumber === order.orderNumber 
            ? { ...o, ...updatedData }
            : o
        )
      );
      toast({
        title: "Pesanan berhasil diperbarui",
        description: `Pesanan ${order.orderNumber} telah diperbarui`,
      });
    } else if (onNavigate) {
      onNavigate();
    } else {
      console.log('Editing order:', order.orderNumber);
    }
  };

  const handleAssignDriver = (order: Order, driverData?: any, onNavigate?: () => void) => {
    if (driverData) {
      // Actually assign the driver
      setOrders(prev =>
        prev.map(o =>
          o.orderNumber === order.orderNumber
            ? { 
                ...o, 
                assignedDriver: driverData.driverName,
                status: 'assigned' as const,
                assignedAt: driverData.assignedAt,
                driverNotes: driverData.notes
              }
            : o
        )
      );
      toast({
        title: "Driver berhasil ditugaskan",
        description: `${driverData.driverName} telah ditugaskan untuk pesanan ${order.orderNumber}`,
      });
    } else if (onNavigate) {
      onNavigate();
    } else {
      console.log('Assigning driver to order:', order.orderNumber);
    }
  };

  return {
    orders,
    handleCreateOrder,
    handleViewOrder,
    handleEditOrder,
    handleAssignDriver
  };
};
