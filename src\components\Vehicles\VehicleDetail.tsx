
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Vehicle } from '@/types/vehicle';
import { ArrowLeft, ChevronLeft, Edit, AlertTriangle } from 'lucide-react';
import { getVehicleTypeBadge, getVehicleSourceBadge, getStatusColor, getStatusText } from '@/lib/badgeUtils';
import { formatDate } from '@/lib/dateUtils';

interface VehicleDetailProps {
  vehicle: Vehicle;
  onBack: () => void;
  onEdit: (vehicle: Vehicle) => void;
}

const VehicleDetail = ({ vehicle, onBack, onEdit }: VehicleDetailProps) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={onBack}>
          <ChevronLeft className="mr-2 h-4 w-4" />
          Kembali
        </Button>
        <Button onClick={() => onEdit(vehicle)}>
          <Edit className="mr-2 h-4 w-4" />
          Edit
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle>{vehicle.plateNumber}</CardTitle>
              <Badge className={`${getStatusColor(vehicle.status)} text-white`}>
                {getStatusText(vehicle.status)}
              </Badge>
            </div>
            <p className="text-sm text-gray-600">{vehicle.brand} {vehicle.model} ({vehicle.year})</p>
            <div className="flex gap-2 mt-2">
              {getVehicleTypeBadge(vehicle.type)}
              {getVehicleSourceBadge(vehicle.vehicleSource)}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Kapasitas</h3>
                <p className="mt-1">{vehicle.capacity.toLocaleString()} kg</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Bahan Bakar</h3>
                <p className="mt-1">{vehicle.fuelType}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Kilometer</h3>
                <p className="mt-1">{vehicle.mileage.toLocaleString()} km</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Kondisi</h3>
                <p className="mt-1">{vehicle.condition}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Informasi Dokumen</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500">STNK Berlaku Hingga</h3>
              <p className="mt-1">{formatDate(vehicle.registrationExpiry)}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Asuransi Berlaku Hingga</h3>
              <p className="mt-1">{formatDate(vehicle.insuranceExpiry)}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Informasi Pemeliharaan</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Pemeliharaan Terakhir</h3>
              <p className="mt-1">{formatDate(vehicle.lastMaintenance)}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Pemeliharaan Berikutnya</h3>
              <p className="mt-1">{formatDate(vehicle.nextMaintenance)}</p>
            </div>
          </CardContent>
        </Card>

        {vehicle.notes && (
          <Card>
            <CardHeader>
              <CardTitle>Catatan</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{vehicle.notes}</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default VehicleDetail;
