
import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Eye, Edit, Truck, Calendar } from 'lucide-react';
import { Order } from '@/types/order';
import { getOrderStatusColor, getOrderStatusText, getOrderPriorityColor, getOrderPriorityText } from '@/lib/badgeUtils';
import { formatDateShort } from '@/lib/dateUtils';

interface OrderListProps {
  orders: Order[];
  userRole: 'admin' | 'financial' | 'operational' | 'driver';
  onViewOrder: (order: Order) => void;
  onEditOrder?: (order: Order) => void;
  onAssignDriver?: (order: Order) => void;
}

const OrderList = ({ orders, userRole, onViewOrder, onEditOrder, onAssignDriver }: OrderListProps) => {

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'low': return 'Rendah';
      case 'normal': return 'Normal';
      case 'high': return 'Tinggi';
      case 'urgent': return 'Mendesak';
      default: return priority;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
        <h2 className="text-xl sm:text-2xl font-bold">Daftar Pesanan</h2>
        <Badge variant="outline" className="text-sm">
          {orders.length} total pesanan
        </Badge>
      </div>

      <ScrollArea className="h-[calc(100vh-240px)] w-full">
        <div className="grid gap-4 pr-4">
          {orders.map((order) => (
            <Card key={order.orderNumber} className="hover:shadow-md transition-shadow w-full">
              <CardHeader className="pb-3">
                <div className="flex flex-col sm:flex-row justify-between items-start gap-3">
                  <div className="min-w-0 flex-1">
                    <CardTitle className="text-base sm:text-lg break-words">{order.orderNumber}</CardTitle>
                    <p className="text-sm text-gray-600 mt-1 break-words">{order.customerName}</p>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <Badge className={getOrderPriorityColor(order.priority)}>
                      {getOrderPriorityText(order.priority)}
                    </Badge>
                    <Badge className={getOrderStatusColor(order.status)}>
                      {getOrderStatusText(order.status)}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-3">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 text-sm">
                    <div className="min-w-0">
                      <p className="font-medium text-gray-700">Dari:</p>
                      <p className="text-gray-600 break-words">{order.pickupAddress}</p>
                    </div>
                    <div className="min-w-0">
                      <p className="font-medium text-gray-700">Ke:</p>
                      <p className="text-gray-600 break-words">{order.deliveryAddress}</p>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row flex-wrap gap-2 sm:gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4 flex-shrink-0" />
                      <span>Jemput: {formatDateShort(order.pickupDate)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4 flex-shrink-0" />
                      <span>Kirim: {formatDateShort(order.deliveryDate)}</span>
                    </div>
                    {order.assignedDriver && (
                      <div className="flex items-center gap-1">
                        <Truck className="h-4 w-4 flex-shrink-0" />
                        <span className="break-words">Driver: {order.assignedDriver}</span>
                      </div>
                    )}
                  </div>

                  {order.amount > 0 && (
                    <div className="text-right">
                      <p className="text-lg font-semibold text-green-600">
                        {formatCurrency(order.amount)}
                      </p>
                    </div>
                  )}

                  <div className="flex flex-col sm:flex-row gap-2 pt-2 border-t">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onViewOrder(order)}
                      className="w-full sm:w-auto"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      Lihat
                    </Button>
                    
                    {(userRole === 'admin' || userRole === 'operational') && onEditOrder && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onEditOrder(order)}
                        className="w-full sm:w-auto"
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    )}
                    
                    {(userRole === 'admin' || userRole === 'operational') && 
                     order.status === 'pending' && onAssignDriver && (
                      <Button
                        size="sm"
                        onClick={() => onAssignDriver(order)}
                        className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto"
                      >
                        <Truck className="h-4 w-4 mr-1" />
                        Tugaskan Driver
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {orders.length === 0 && (
          <Card className="w-full">
            <CardContent className="text-center py-8">
              <p className="text-gray-500">Belum ada pesanan</p>
            </CardContent>
          </Card>
        )}
      </ScrollArea>
    </div>
  );
};

export default OrderList;
