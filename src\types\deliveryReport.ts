
export interface DeliveryReport {
  id: string;
  orderId: string;
  orderNumber: string;
  driverType: 'internal' | 'vendor';
  driverId: string;
  driverName: string;
  status: 'draft' | 'loading-reported' | 'unloading-reported' | 'document-delivered' | 'completed';
  createdAt: Date;
  updatedAt: Date;
  
  // Internal driver data
  internalData?: {
    pickupDate: Date;
    customerName: string;
    waybillNumber: string;
    destinationCompany: string;
    destinationArea?: string;
    waybillPhotos?: File[];
  };
  
  // Vendor driver loading data
  loadingData?: {
    vehicleId: string;
    pickupDate: Date;
    loadingPhotos: File[];
    loadingVideo?: File;
    waybillPhotosLoading: File[];
    reportedAt: Date;
  };
  
  // Vendor driver unloading data
  unloadingData?: {
    unloadingDate: Date;
    unloadingPhotos: File[];
    unloadingVideo?: File;
    waybillPhotosUnloading: File[];
    reportedAt: Date;
  };
}

export interface DeliveryReportFormData {
  orderId?: string;
  reportId?: string;
  driverType: 'internal' | 'vendor';
  
  // Common fields
  pickupDate?: Date;
  
  // Internal driver fields
  customerName?: string;
  waybillNumber?: string;
  destinationCompany?: string;
  destinationArea?: string;
  waybillPhotos?: File[];
  
  // Vendor driver loading fields
  vehicleId?: string;
  loadingPhotos?: File[];
  loadingVideo?: File;
  waybillPhotosLoading?: File[];
  
  // Vendor driver unloading fields
  unloadingDate?: Date;
  unloadingPhotos?: File[];
  unloadingVideo?: File;
  waybillPhotosUnloading?: File[];
}
