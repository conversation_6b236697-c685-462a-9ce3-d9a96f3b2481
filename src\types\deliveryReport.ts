
export interface DeliveryReport {
  id: string;
  orderId: string;
  orderNumber: string;
  driverType: 'internal' | 'vendor';
  driverId: string;
  driverName: string;
  status: 'draft' | 'loading-reported' | 'unloading-reported' | 'document-delivered' | 'completed';
  createdAt: Date;
  updatedAt: Date;
  
  // Internal driver (satu tahap langsung)
  internalData?: {
    pickupDate: Date;
    customerName: string;
    waybillNumber: string;
    destinationCompany: string;
  };
  
  // Vendor driver tahap 1 (saat muat)
  loadingData?: {
    vehicleId: string;
    pickupDate: Date;
    loadingPhotos: File[];
    loadingVideo?: File;
    waybillPhotosLoading: File[]; // 2 foto surat jalan
    reportedAt: Date;
  };
  
  // Vendor driver tahap 2 (saat bongkar)
  unloadingData?: {
    unloadingDate: Date;
    unloadingPhotos: File[];
    unloadingVideo?: File;
    waybillPhotosUnloading: File[]; // 2 foto surat jalan
    reportedAt: Date;
  };
  
  // Vendor driver tahap 3 (pengiriman surat jalan)
  documentDeliveryData?: {
    courierName: string;
    receiptPhoto: File;
    deliveryDate: Date;
    reportedAt: Date;
  };
}

export interface DeliveryReportFormData {
  orderId: string;
  driverType: 'internal' | 'vendor';
  
  // Internal driver data
  pickupDate?: Date;
  customerName?: string;
  waybillNumber?: string;
  destinationCompany?: string;
  
  // Vendor loading data
  vehicleId?: string;
  loadingPhotos?: File[];
  loadingVideo?: File;
  waybillPhotosLoading?: File[];
  
  // Vendor unloading data
  unloadingDate?: Date;
  unloadingPhotos?: File[];
  unloadingVideo?: File;
  waybillPhotosUnloading?: File[];
  
  // Document delivery data
  courierName?: string;
  receiptPhoto?: File;
  deliveryDate?: Date;
}
