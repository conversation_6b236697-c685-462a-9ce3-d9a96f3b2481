
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Driver, DriverFormData } from '@/types/driver';
import { mockDrivers } from '@/data/mockDrivers';

export const useDriverManagement = () => {
  const [drivers, setDrivers] = useState<Driver[]>(mockDrivers);
  const { toast } = useToast();

  const handleCreateDriver = (driverData: DriverFormData) => {
    const newDriver: Driver = {
      ...driverData,
      id: `DRV-${String(drivers.length + 1).padStart(3, '0')}`,
      status: 'active',
      rating: 0,
      totalDeliveries: 0,
      joinDate: new Date()
    };
    
    setDrivers(prev => [newDriver, ...prev]);
    toast({
      title: "Driver berhasil ditambahkan",
      description: `${newDriver.name} telah ditambahkan ke sistem`,
    });
  };

  const handleUpdateDriver = (driverId: string, updatedData: Partial<DriverFormData>) => {
    setDrivers(prev =>
      prev.map(driver =>
        driver.id === driverId
          ? { ...driver, ...updatedData }
          : driver
      )
    );
    toast({
      title: "Driver berhasil diperbarui",
      description: "Informasi driver telah diperbarui",
    });
  };

  const handleToggleDriverStatus = (driverId: string) => {
    setDrivers(prev =>
      prev.map(driver =>
        driver.id === driverId
          ? { 
              ...driver, 
              status: driver.status === 'active' ? 'inactive' : 'active'
            }
          : driver
      )
    );
    
    const driver = drivers.find(d => d.id === driverId);
    if (driver) {
      toast({
        title: "Status driver diperbarui",
        description: `${driver.name} sekarang ${driver.status === 'active' ? 'nonaktif' : 'aktif'}`,
      });
    }
  };

  const handleDeleteDriver = (driverId: string) => {
    const driver = drivers.find(d => d.id === driverId);
    setDrivers(prev => prev.filter(d => d.id !== driverId));
    
    if (driver) {
      toast({
        title: "Driver dihapus",
        description: `${driver.name} telah dihapus dari sistem`,
      });
    }
  };

  return {
    drivers,
    handleCreateDriver,
    handleUpdateDriver,
    handleToggleDriverStatus,
    handleDeleteDriver
  };
};
