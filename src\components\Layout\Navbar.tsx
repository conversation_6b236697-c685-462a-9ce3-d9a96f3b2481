
import React from 'react';
import { User, LogOut, Truck, Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface NavbarProps {
  user: {
    name: string;
    role: 'admin' | 'financial' | 'operational' | 'driver';
  };
  onLogout: () => void;
}

const Navbar = ({ user, onLogout }: NavbarProps) => {
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-500';
      case 'financial': return 'bg-green-500';
      case 'operational': return 'bg-blue-500';
      case 'driver': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <nav className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-14 sm:h-16">
          <div className="flex items-center min-w-0 flex-1">
            <div className="flex-shrink-0 flex items-center">
              <Truck className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
              <span className="ml-2 text-sm sm:text-xl font-bold text-gray-900 truncate">
                PT. KARYA INTI PRESISI
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2 sm:space-x-4">
            <div className="flex items-center space-x-1 sm:space-x-2 min-w-0">
              <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full flex-shrink-0 ${getRoleColor(user.role)}`}></div>
              <span className="text-xs sm:text-sm font-medium text-gray-700 truncate max-w-20 sm:max-w-none">{user.name}</span>
              <span className="text-xs text-gray-500 capitalize hidden sm:inline">({user.role})</span>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={onLogout}
              className="flex items-center space-x-1 text-xs sm:text-sm px-2 sm:px-3"
            >
              <LogOut className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Keluar</span>
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
