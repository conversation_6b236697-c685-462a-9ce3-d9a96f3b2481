import React from 'react';
import DashboardStats from '@/components/Dashboard/DashboardStats';

interface DashboardSectionProps {
  user: { name: string; role: 'admin' | 'financial' | 'operational' | 'driver' };
}

const DashboardSection = ({ user }: DashboardSectionProps) => {
  return (
    <div className="space-y-4 sm:space-y-6 max-w-full">
      <div className="space-y-2">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-sm sm:text-base text-gray-600">Selamat datang di Sistem Manajemen Transportasi</p>
      </div>
      <DashboardStats userRole={user.role} />
    </div>
  );
};

export default DashboardSection;
