
import React from 'react';
import { Driver } from '@/types/driver';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ArrowLeft,
  Edit,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Star,
  Truck,
  User,
  AlertTriangle
} from 'lucide-react';
import { getDriverStatusBadge, getDriverTypeBadge } from '@/lib/badgeUtils';
import { formatDate, isDateExpired, isDateExpiringSoon } from '@/lib/dateUtils';

interface DriverDetailProps {
  driver: Driver;
  onBack: () => void;
  onEdit: () => void;
}

const DriverDetail = ({ driver, onBack, onEdit }: DriverDetailProps) => {

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={onBack} className="flex items-center gap-2">
            <ArrowLeft className="w-4 h-4" />
            <span className="hidden sm:inline">Kembali</span>
          </Button>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold">{driver.name}</h1>
            <p className="text-gray-600">{driver.id}</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          {getDriverStatusBadge(driver.status)}
          {getDriverTypeBadge(driver.driverType)}
          <Button onClick={onEdit} className="flex items-center gap-2">
            <Edit className="w-4 h-4" />
            <span className="hidden sm:inline">Edit Driver</span>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Information */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Informasi Pribadi
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Phone className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Nomor Telepon</p>
                    <p className="font-medium">{driver.phone}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Mail className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{driver.email}</p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <MapPin className="w-4 h-4 text-gray-500 mt-1" />
                <div>
                  <p className="text-sm text-gray-500">Alamat</p>
                  <p className="font-medium">{driver.address}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Calendar className="w-4 h-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Bergabung Sejak</p>
                  <p className="font-medium">{formatDate(driver.joinDate)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Truck className="w-5 h-5" />
                Informasi SIM & Kendaraan
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Nomor SIM</p>
                  <p className="font-medium">{driver.licenseNumber}</p>
                </div>
                
                <div>
                  <p className="text-sm text-gray-500">Tanggal Kadaluarsa</p>
                  <div className="flex items-center gap-2">
                    <p className="font-medium">{formatDate(driver.licenseExpiry)}</p>
                    {isDateExpired(driver.licenseExpiry) && (
                      <Badge variant="destructive" className="text-xs">
                        <AlertTriangle className="w-3 h-3 mr-1" />
                        Expired
                      </Badge>
                    )}
                    {isDateExpiringSoon(driver.licenseExpiry) && !isDateExpired(driver.licenseExpiry) && (
                      <Badge variant="secondary" className="text-xs">
                        <AlertTriangle className="w-3 h-3 mr-1" />
                        Segera Habis
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
              
              <div>
                <p className="text-sm text-gray-500 mb-2">Jenis Kendaraan</p>
                <div className="flex flex-wrap gap-2">
                  {driver.vehicleTypes.map((type, index) => (
                    <Badge key={index} variant="outline">{type}</Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {driver.emergencyContact && (
            <Card>
              <CardHeader>
                <CardTitle>Kontak Darurat</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Nama</p>
                    <p className="font-medium">{driver.emergencyContact.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Nomor Telepon</p>
                    <p className="font-medium">{driver.emergencyContact.phone}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Hubungan</p>
                    <p className="font-medium">{driver.emergencyContact.relation}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Performance Stats */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="w-5 h-5" />
                Performa
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">{driver.rating}</div>
                <div className="text-sm text-gray-500">Rating Driver</div>
                <div className="flex justify-center mt-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${
                        i < Math.floor(driver.rating)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
              </div>
              
              <div className="text-center pt-4 border-t">
                <div className="text-2xl font-bold text-green-600">{driver.totalDeliveries}</div>
                <div className="text-sm text-gray-500">Total Pengiriman</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Status Driver</span>
                  {getDriverStatusBadge(driver.status)}
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm">Tipe Driver</span>
                  {getDriverTypeBadge(driver.driverType)}
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm">SIM</span>
                  <Badge 
                    variant={isLicenseExpired() ? "destructive" : isLicenseExpiring() ? "secondary" : "default"}
                  >
                    {isLicenseExpired() ? "Expired" : isLicenseExpiring() ? "Segera Habis" : "Valid"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default DriverDetail;
