
import React from 'react';
import { Driver } from '@/types/driver';
import { Eye, Edit, UserCheck, UserX, Phone, Mail, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { getDriverStatusBadge, getDriverTypeBadge } from '@/lib/badgeUtils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface DriverListProps {
  drivers: Driver[];
  onViewDriver: (driver: Driver) => void;
  onEditDriver: (driver: Driver) => void;
  onToggleStatus: (driverId: string) => void;
}

const DriverList = ({ drivers, onViewDriver, onEditDriver, onToggleStatus }: DriverListProps) => {

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  return (
    <div className="space-y-4">
      {/* Mobile Card View */}
      <div className="block md:hidden space-y-4">
        {drivers.map((driver) => (
          <div key={driver.id} className="bg-white border rounded-lg p-4 shadow-sm">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="font-semibold text-lg">{driver.name}</h3>
                <p className="text-sm text-gray-600">{driver.id}</p>
              </div>
              <div className="flex flex-col gap-1 items-end">
                {getDriverStatusBadge(driver.status)}
                {getDriverTypeBadge(driver.driverType)}
              </div>
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 text-gray-500" />
                <span>{driver.phone}</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-gray-500" />
                <span>{driver.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-500" />
                <span>SIM: {formatDate(driver.licenseExpiry)}</span>
              </div>
            </div>
            
            <div className="flex justify-between items-center mt-4 pt-3 border-t">
              <div className="text-sm">
                <span className="font-medium">Rating: {driver.rating}/5</span>
                <span className="text-gray-500 ml-2">({driver.totalDeliveries} pengiriman)</span>
              </div>
              <div className="flex gap-2">
                <Button size="sm" variant="outline" onClick={() => onViewDriver(driver)}>
                  <Eye className="w-4 h-4" />
                </Button>
                <Button size="sm" variant="outline" onClick={() => onEditDriver(driver)}>
                  <Edit className="w-4 h-4" />
                </Button>
                <Button 
                  size="sm" 
                  variant={driver.status === 'active' ? 'destructive' : 'default'}
                  onClick={() => onToggleStatus(driver.id)}
                >
                  {driver.status === 'active' ? <UserX className="w-4 h-4" /> : <UserCheck className="w-4 h-4" />}
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Desktop Table View */}
      <div className="hidden md:block">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Driver</TableHead>
              <TableHead>Kontak</TableHead>
              <TableHead>SIM</TableHead>
              <TableHead>Tipe</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Performa</TableHead>
              <TableHead>Aksi</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {drivers.map((driver) => (
              <TableRow key={driver.id}>
                <TableCell>
                  <div>
                    <div className="font-medium">{driver.name}</div>
                    <div className="text-sm text-gray-500">{driver.id}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="text-sm">{driver.phone}</div>
                    <div className="text-sm text-gray-500">{driver.email}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="text-sm">{driver.licenseNumber}</div>
                    <div className="text-sm text-gray-500">
                      Exp: {formatDate(driver.licenseExpiry)}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  {getDriverTypeBadge(driver.driverType)}
                </TableCell>
                <TableCell>
                  {getDriverStatusBadge(driver.status)}
                </TableCell>
                <TableCell>
                  <div>
                    <div className="text-sm font-medium">⭐ {driver.rating}/5</div>
                    <div className="text-sm text-gray-500">{driver.totalDeliveries} pengiriman</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => onViewDriver(driver)}>
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => onEditDriver(driver)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button 
                      size="sm" 
                      variant={driver.status === 'active' ? 'destructive' : 'default'}
                      onClick={() => onToggleStatus(driver.id)}
                    >
                      {driver.status === 'active' ? <UserX className="w-4 h-4" /> : <UserCheck className="w-4 h-4" />}
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default DriverList;
