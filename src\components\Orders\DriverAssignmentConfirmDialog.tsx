
import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Loader2 } from 'lucide-react';
import DriverAssignmentSummary from './DriverAssignmentSummary';

interface Driver {
  id: string;
  name: string;
  phone: string;
  vehicle: string;
  experience: string;
}

interface DriverAssignmentConfirmDialogProps {
  isOpen: boolean;
  driver: Driver | null;
  orderNumber: string;
  notes: string;
  isLoading: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

const DriverAssignmentConfirmDialog = ({
  isOpen,
  driver,
  orderNumber,
  notes,
  isLoading,
  onConfirm,
  onCancel
}: DriverAssignmentConfirmDialogProps) => {
  return (
    <AlertDialog open={isOpen} onOpenChange={() => !isLoading && onCancel()}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle>Konfirmasi Penugasan Driver</AlertDialogTitle>
          <AlertDialogDescription className="space-y-2">
            <p>Apakah Anda yakin ingin menugaskan driver berikut?</p>
            {driver && (
              <DriverAssignmentSummary
                driver={driver}
                orderNumber={orderNumber}
                notes={notes}
              />
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex-col-reverse sm:flex-row gap-2">
          <AlertDialogCancel 
            onClick={onCancel} 
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            Batal
          </AlertDialogCancel>
          <AlertDialogAction 
            onClick={onConfirm}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Memproses...
              </>
            ) : (
              'Ya, Tugaskan Driver'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DriverAssignmentConfirmDialog;
