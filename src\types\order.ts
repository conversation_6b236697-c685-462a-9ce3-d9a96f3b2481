
export interface Order {
  orderNumber: string;
  customerName: string;
  pickupAddress: string;
  deliveryAddress: string;
  pickupDate: Date;
  deliveryDate: Date;
  status: 'pending' | 'assigned' | 'in-progress' | 'completed' | 'invoiced';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  assignedDriver?: string;
  amount: number;
  cargoDescription?: string;
  assignedAt?: Date;
  driverNotes?: string;
}
