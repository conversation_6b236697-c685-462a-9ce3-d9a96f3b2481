import React from 'react';
import ReportDashboard from '@/components/Reports/ReportDashboard';
import { ReportStats, OrderReportData, DriverReportData, VehicleReportData } from '@/types/report';

interface ReportSectionProps {
  reportStats: ReportStats;
  orderReportData: OrderReportData[];
  driverReportData: DriverReportData[];
  vehicleReportData: VehicleReportData[];
}

const ReportSection = ({
  reportStats,
  orderReportData,
  driverReportData,
  vehicleReportData,
}: ReportSectionProps) => {
  return (
    <div className="max-w-full">
      <div className="space-y-4 sm:space-y-6">
        <div className="space-y-1">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900"><PERSON><PERSON><PERSON></h1>
          <p className="text-sm sm:text-base text-gray-600">Dashboard laporan dan analitik sistem</p>
        </div>
        <ReportDashboard
          stats={reportStats}
          orderData={orderReportData}
          driverData={driverReportData}
          vehicleData={vehicleReportData}
        />
      </div>
    </div>
  );
};

export default ReportSection;
