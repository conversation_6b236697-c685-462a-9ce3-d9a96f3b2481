
import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';

interface Driver {
  id: string;
  name: string;
  phone: string;
  vehicle: string;
  experience: string;
}

interface DriverAssignmentSummaryProps {
  driver: Driver;
  orderNumber: string;
  notes?: string;
}

const DriverAssignmentSummary = ({ driver, orderNumber, notes }: DriverAssignmentSummaryProps) => {
  return (
    <ScrollArea className="max-h-40">
      <div className="bg-gray-50 p-3 rounded-md space-y-1">
        <p className="truncate"><strong>Driver:</strong> {driver.name}</p>
        <p className="truncate"><strong>Telepon:</strong> {driver.phone}</p>
        <p className="truncate"><strong>Kendaraan:</strong> {driver.vehicle}</p>
        <p className="truncate"><strong>Pesanan:</strong> {orderNumber}</p>
        {notes && <p><strong>Catatan:</strong> {notes}</p>}
      </div>
    </ScrollArea>
  );
};

export default DriverAssignmentSummary;
