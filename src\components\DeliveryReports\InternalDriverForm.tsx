
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { mockCustomers } from '@/data/mockCustomers';
import { DeliveryReportFormData } from '@/types/deliveryReport';
import { useToast } from '@/hooks/use-toast';

interface InternalDriverFormProps {
  orderId: string;
  orderNumber: string;
  onSubmit: (data: DeliveryReportFormData) => void;
  onCancel: () => void;
}

// Mock destination companies
const mockCompanies = [
  'PT. Logistik Prima',
  'CV. Express Delivery',
  'PT. Fast Courier',
  'UD. Reliable Transport',
  'PT. Swift Logistics'
];

const InternalDriverForm = ({ orderId, orderNumber, onSubmit, onCancel }: InternalDriverFormProps) => {
  const [formData, setFormData] = useState<DeliveryReportFormData>({
    orderId,
    driverType: 'internal',
    pickupDate: new Date(),
    customerName: '',
    waybillNumber: '',
    destinationCompany: ''
  });

  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.customerName || !formData.waybillNumber || !formData.destinationCompany) {
      toast({
        title: "Form tidak lengkap",
        description: "Mohon isi semua field yang diperlukan",
        variant: "destructive"
      });
      return;
    }

    onSubmit(formData);
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl font-semibold">
            Lapor Pengiriman - Driver Internal
          </CardTitle>
          <p className="text-sm text-gray-600">
            Pesanan: {orderNumber}
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="pickupDate">Tanggal Pickup/Jemput</Label>
              <Input
                id="pickupDate"
                type="date"
                value={formData.pickupDate?.toISOString().split('T')[0] || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  pickupDate: new Date(e.target.value)
                }))}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="customerName">Nama Customer</Label>
              <Select
                value={formData.customerName}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  customerName: value
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih customer" />
                </SelectTrigger>
                <SelectContent>
                  {mockCustomers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.companyName}>
                      {customer.companyName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="waybillNumber">Nomor Surat Jalan</Label>
              <Input
                id="waybillNumber"
                type="text"
                value={formData.waybillNumber || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  waybillNumber: e.target.value
                }))}
                placeholder="Masukkan nomor surat jalan"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="destinationCompany">Perusahaan Tujuan Pengiriman</Label>
              <Select
                value={formData.destinationCompany}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  destinationCompany: value
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Pilih perusahaan tujuan" />
                </SelectTrigger>
                <SelectContent>
                  {mockCompanies.map((company) => (
                    <SelectItem key={company} value={company}>
                      {company}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button type="submit" className="flex-1">
                Kirim Laporan
              </Button>
              <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
                Batal
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default InternalDriverForm;
