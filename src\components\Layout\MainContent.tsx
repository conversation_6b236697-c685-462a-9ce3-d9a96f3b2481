import React from 'react';
import DashboardStats from '@/components/Dashboard/DashboardStats';
import OrderForm from '@/components/Orders/OrderForm';
import OrderList from '@/components/Orders/OrderList';
import OrderDetail from '@/components/Orders/OrderDetail';
import AssignDriverForm from '@/components/Orders/AssignDriverForm';
import DriverList from '@/components/Drivers/DriverList';
import DriverForm from '@/components/Drivers/DriverForm';
import DriverDetail from '@/components/Drivers/DriverDetail';
import VehicleList from '@/components/Vehicles/VehicleList';
import VehicleForm from '@/components/Vehicles/VehicleForm';
import VehicleDetail from '@/components/Vehicles/VehicleDetail';
import InvoiceList from '@/components/Invoices/InvoiceList';
import InvoiceForm from '@/components/Invoices/InvoiceForm';
import InvoiceDetail from '@/components/Invoices/InvoiceDetail';
import ReportDashboard from '@/components/Reports/ReportDashboard';
import DeliveryReportList from '@/components/DeliveryReports/DeliveryReportList';
import DeliveryReportForm from '@/components/DeliveryReports/DeliveryReportForm';
import { Order } from '@/types/order';
import { Driver } from '@/types/driver';
import { Vehicle } from '@/types/vehicle';
import { Invoice } from '@/types/invoice';
import { DeliveryReport, DeliveryReportFormData } from '@/types/deliveryReport';
import { ReportStats, OrderReportData, DriverReportData, VehicleReportData } from '@/types/report';

interface MainContentProps {
  activeTab: string;
  showOrderForm: boolean;
  currentView: string;
  selectedOrder: Order | null;
  selectedDriver: Driver | null;
  selectedVehicle: Vehicle | null;
  selectedInvoice: Invoice | null;
  showDriverForm: boolean;
  showVehicleForm: boolean;
  showInvoiceForm: boolean;
  showAssignDriverModal: boolean;
  showDeliveryReportForm: boolean;
  selectedDeliveryReport: DeliveryReport | null;
  deliveryReportStage: 'loading' | 'unloading' | 'document-delivery';
  user: { name: string; role: 'admin' | 'financial' | 'operational' | 'driver'; driverType?: 'internal' | 'vendor' };
  orders: Order[];
  drivers: Driver[];
  vehicles: Vehicle[];
  invoices: Invoice[];
  deliveryReports: DeliveryReport[];
  reportStats: ReportStats;
  orderReportData: OrderReportData[];
  driverReportData: DriverReportData[];
  vehicleReportData: VehicleReportData[];
  onCreateOrder: (orderData: any) => void;
  onCancelOrderForm: () => void;
  onShowOrderForm: () => void;
  onViewOrder: (order: Order) => void;
  onEditOrder: (order: Order) => void;
  onAssignDriver: (order: Order) => void;
  onBackToList: () => void;
  onCloseAssignDriverModal: () => void;
  onAssignDriverComplete: (driverData: any) => void;
  onUpdateOrder: (orderData: any) => void;
  // Driver management props
  onCreateDriver: (driverData: any) => void;
  onViewDriver: (driver: Driver) => void;
  onEditDriver: (driver: Driver) => void;
  onToggleDriverStatus: (driverId: string) => void;
  onShowDriverForm: () => void;
  onCancelDriverForm: () => void;
  onUpdateDriver: (driverData: any) => void;
  onBackToDriverList: () => void;
  // Vehicle management props
  onCreateVehicle: (vehicleData: any) => void;
  onViewVehicle: (vehicle: Vehicle) => void;
  onEditVehicle: (vehicle: Vehicle) => void;
  onToggleVehicleStatus: (vehicleId: string) => void;
  onShowVehicleForm: () => void;
  onCancelVehicleForm: () => void;
  onUpdateVehicle: (vehicleData: any) => void;
  onBackToVehicleList: () => void;
  // Invoice management props
  onCreateInvoice: (invoiceData: any) => void;
  onViewInvoice: (invoice: Invoice) => void;
  onEditInvoice: (invoice: Invoice) => void;
  onUpdateInvoiceStatus: (invoiceId: string, status: Invoice['status']) => void;
  onShowInvoiceForm: () => void;
  onCancelInvoiceForm: () => void;
  onUpdateInvoice: (invoiceData: any) => void;
  onBackToInvoiceList: () => void;
  // Delivery report props
  onCreateDeliveryReport: (orderId: string) => void;
  onContinueDeliveryReport: (report: DeliveryReport, stage: 'unloading' | 'document-delivery') => void;
  onSubmitDeliveryReport: (data: DeliveryReportFormData) => void;
  onCancelDeliveryReportForm: () => void;
  onBackToDeliveryReportList: () => void;
}

const MainContent = ({ 
  activeTab, 
  showOrderForm,
  currentView,
  selectedOrder,
  selectedDriver,
  selectedVehicle,
  selectedInvoice,
  showDriverForm,
  showVehicleForm,
  showInvoiceForm,
  showAssignDriverModal,
  showDeliveryReportForm,
  selectedDeliveryReport,
  deliveryReportStage,
  user, 
  orders,
  drivers,
  vehicles,
  invoices,
  deliveryReports,
  reportStats,
  orderReportData,
  driverReportData,
  vehicleReportData,
  onCreateOrder, 
  onCancelOrderForm, 
  onShowOrderForm,
  onViewOrder,
  onEditOrder,
  onAssignDriver,
  onBackToList,
  onCloseAssignDriverModal,
  onAssignDriverComplete,
  onUpdateOrder,
  onCreateDriver,
  onViewDriver,
  onEditDriver,
  onToggleDriverStatus,
  onShowDriverForm,
  onCancelDriverForm,
  onUpdateDriver,
  onBackToDriverList,
  onCreateVehicle,
  onViewVehicle,
  onEditVehicle,
  onToggleVehicleStatus,
  onShowVehicleForm,
  onCancelVehicleForm,
  onUpdateVehicle,
  onBackToVehicleList,
  onCreateInvoice,
  onViewInvoice,
  onEditInvoice,
  onUpdateInvoiceStatus,
  onShowInvoiceForm,
  onCancelInvoiceForm,
  onUpdateInvoice,
  onBackToInvoiceList,
  onCreateDeliveryReport,
  onContinueDeliveryReport,
  onSubmitDeliveryReport,
  onCancelDeliveryReportForm,
  onBackToDeliveryReportList
}: MainContentProps) => {
  // Handle order form display
  if (showOrderForm) {
    return (
      <div className="max-w-full overflow-auto">
        <OrderForm
          onSubmit={onCreateOrder}
          onCancel={onCancelOrderForm}
        />
      </div>
    );
  }

  // Handle delivery report form display
  if (showDeliveryReportForm && selectedDeliveryReport) {
    return (
      <div className="max-w-full overflow-auto">
        <DeliveryReportForm
          orderId={selectedDeliveryReport.orderId}
          orderNumber={selectedDeliveryReport.orderNumber}
          driverType={user.driverType || 'internal'}
          stage={deliveryReportStage}
          onSubmit={onSubmitDeliveryReport}
          onCancel={onCancelDeliveryReportForm}
        />
      </div>
    );
  }

  // Handle order detail view
  if (currentView === 'order-detail' && selectedOrder) {
    return (
      <div className="max-w-full overflow-auto">
        <OrderDetail
          order={selectedOrder}
          onBack={onBackToList}
        />
      </div>
    );
  }

  // Handle order edit view
  if (currentView === 'order-edit' && selectedOrder) {
    return (
      <div className="max-w-full overflow-auto">
        <OrderForm
          onSubmit={onUpdateOrder}
          onCancel={onBackToList}
          initialOrder={selectedOrder}
          isEdit={true}
        />
      </div>
    );
  }

  // Handle driver form display
  if (showDriverForm) {
    return (
      <div className="max-w-full overflow-auto">
        <DriverForm
          driver={currentView === 'driver-edit' ? selectedDriver : null}
          onSubmit={currentView === 'driver-edit' ? onUpdateDriver : onCreateDriver}
          onCancel={currentView === 'driver-edit' ? onBackToDriverList : onCancelDriverForm}
          isEdit={currentView === 'driver-edit'}
        />
      </div>
    );
  }

  // Handle driver detail view
  if (currentView === 'driver-detail' && selectedDriver) {
    return (
      <div className="max-w-full overflow-auto">
        <DriverDetail
          driver={selectedDriver}
          onBack={onBackToDriverList}
          onEdit={() => onEditDriver(selectedDriver)}
        />
      </div>
    );
  }

  // Handle vehicle form display
  if (showVehicleForm) {
    return (
      <div className="max-w-full overflow-auto">
        <VehicleForm
          vehicle={currentView === 'vehicle-edit' ? selectedVehicle : null}
          onSubmit={currentView === 'vehicle-edit' ? onUpdateVehicle : onCreateVehicle}
          onCancel={currentView === 'vehicle-edit' ? onBackToVehicleList : onCancelVehicleForm}
          isEdit={currentView === 'vehicle-edit'}
        />
      </div>
    );
  }

  // Handle vehicle detail view
  if (currentView === 'vehicle-detail' && selectedVehicle) {
    return (
      <div className="max-w-full overflow-auto">
        <VehicleDetail
          vehicle={selectedVehicle}
          onBack={onBackToVehicleList}
          onEdit={() => onEditVehicle(selectedVehicle)}
        />
      </div>
    );
  }

  // Handle invoice form display
  if (showInvoiceForm) {
    return (
      <div className="max-w-full overflow-auto">
        <InvoiceForm
          invoice={currentView === 'invoice-edit' ? selectedInvoice : null}
          onSubmit={currentView === 'invoice-edit' ? onUpdateInvoice : onCreateInvoice}
          onCancel={currentView === 'invoice-edit' ? onBackToInvoiceList : onCancelInvoiceForm}
          isEdit={currentView === 'invoice-edit'}
        />
      </div>
    );
  }

  // Handle invoice detail view
  if (currentView === 'invoice-detail' && selectedInvoice) {
    return (
      <div className="max-w-full overflow-auto">
        <InvoiceDetail
          invoice={selectedInvoice}
          onBack={onBackToInvoiceList}
          onEdit={() => onEditInvoice(selectedInvoice)}
        />
      </div>
    );
  }

  switch (activeTab) {
    case 'dashboard':
      return (
        <div className="space-y-4 sm:space-y-6 max-w-full">
          <div className="space-y-2">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-sm sm:text-base text-gray-600">Selamat datang di Sistem Manajemen Transportasi</p>
          </div>
          <DashboardStats userRole={user.role} />
        </div>
      );
    
    case 'orders':
    case 'my-orders':
      return (
        <div className="max-w-full">
          <div className="space-y-4 sm:space-y-6">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
              <div className="space-y-1">
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                  {activeTab === 'my-orders' ? 'Pesanan Saya' : 'Kelola Pesanan'}
                </h1>
                <p className="text-sm sm:text-base text-gray-600">
                  {activeTab === 'my-orders' ? 'Lihat pesanan yang ditugaskan kepada Anda' : 'Kelola semua pesanan transportasi'}
                </p>
              </div>
              {(user.role === 'admin' || user.role === 'operational') && (
                <button
                  onClick={onShowOrderForm}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium text-sm sm:text-base w-full sm:w-auto"
                >
                  + Buat Pesanan Baru
                </button>
              )}
            </div>
            <OrderList
              orders={orders}
              userRole={user.role}
              onViewOrder={onViewOrder}
              onEditOrder={onEditOrder}
              onAssignDriver={onAssignDriver}
            />
          </div>
          
          {/* Assign Driver Modal */}
          <AssignDriverForm
            isOpen={showAssignDriverModal}
            order={selectedOrder}
            onClose={onCloseAssignDriverModal}
            onAssign={onAssignDriverComplete}
          />
        </div>
      );

    case 'drivers':
      return (
        <div className="max-w-full">
          <div className="space-y-4 sm:space-y-6">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
              <div className="space-y-1">
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Kelola Driver</h1>
                <p className="text-sm sm:text-base text-gray-600">Kelola semua driver dalam sistem</p>
              </div>
              {(user.role === 'admin' || user.role === 'operational') && (
                <button
                  onClick={onShowDriverForm}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium text-sm sm:text-base w-full sm:w-auto"
                >
                  + Tambah Driver Baru
                </button>
              )}
            </div>
            <DriverList
              drivers={drivers}
              onViewDriver={onViewDriver}
              onEditDriver={onEditDriver}
              onToggleStatus={onToggleDriverStatus}
            />
          </div>
        </div>
      );

    case 'vehicles':
      return (
        <div className="max-w-full">
          <div className="space-y-4 sm:space-y-6">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
              <div className="space-y-1">
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Kelola Kendaraan</h1>
                <p className="text-sm sm:text-base text-gray-600">Kelola semua kendaraan dalam sistem</p>
              </div>
              {(user.role === 'admin' || user.role === 'operational') && (
                <button
                  onClick={onShowVehicleForm}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium text-sm sm:text-base w-full sm:w-auto"
                >
                  + Tambah Kendaraan Baru
                </button>
              )}
            </div>
            <VehicleList
              vehicles={vehicles}
              onViewVehicle={onViewVehicle}
              onEditVehicle={onEditVehicle}
              onToggleStatus={onToggleVehicleStatus}
            />
          </div>
        </div>
      );

    case 'invoices':
      return (
        <div className="max-w-full">
          <div className="space-y-4 sm:space-y-6">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
              <div className="space-y-1">
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Kelola Invoice</h1>
                <p className="text-sm sm:text-base text-gray-600">Kelola semua invoice dalam sistem</p>
              </div>
              {(user.role === 'admin' || user.role === 'financial') && (
                <button
                  onClick={onShowInvoiceForm}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium text-sm sm:text-base w-full sm:w-auto"
                >
                  + Buat Invoice Baru
                </button>
              )}
            </div>
            <InvoiceList
              invoices={invoices}
              onViewInvoice={onViewInvoice}
              onEditInvoice={onEditInvoice}
              onUpdateStatus={onUpdateInvoiceStatus}
            />
          </div>
        </div>
      );

    case 'delivery-report':
      return (
        <div className="max-w-full">
          <DeliveryReportList
            reports={deliveryReports}
            orders={orders}
            driverType={user.driverType || 'internal'}
            onCreateReport={onCreateDeliveryReport}
            onContinueReport={onContinueDeliveryReport}
          />
        </div>
      );

    case 'reports':
      return (
        <div className="max-w-full">
          <div className="space-y-4 sm:space-y-6">
            <div className="space-y-1">
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Laporan</h1>
              <p className="text-sm sm:text-base text-gray-600">Dashboard laporan dan analitik sistem</p>
            </div>
            <ReportDashboard
              stats={reportStats}
              orderData={orderReportData}
              driverData={driverReportData}
              vehicleData={vehicleReportData}
            />
          </div>
        </div>
      );
    
    default:
      return (
        <div className="text-center py-8 max-w-full">
          <h2 className="text-xl sm:text-2xl font-bold text-gray-900">
            Fitur {activeTab} sedang dalam pengembangan
          </h2>
          <p className="text-sm sm:text-base text-gray-600 mt-2">
            Fitur ini akan segera tersedia
          </p>
        </div>
      );
  }
};

export default MainContent;
