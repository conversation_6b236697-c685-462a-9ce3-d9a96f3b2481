import React from 'react';
import DashboardSection from './sections/DashboardSection';
import OrderSection from './sections/OrderSection';
import DriverSection from './sections/DriverSection';
import VehicleSection from './sections/VehicleSection';
import InvoiceSection from './sections/InvoiceSection';
import DeliveryReportSection from './sections/DeliveryReportSection';
import ReportSection from './sections/ReportSection';
import { Order } from '@/types/order';
import { Driver } from '@/types/driver';
import { Vehicle } from '@/types/vehicle';
import { Invoice } from '@/types/invoice';
import { DeliveryReport, DeliveryReportFormData } from '@/types/deliveryReport';
import { ReportStats, OrderReportData, DriverReportData, VehicleReportData } from '@/types/report';

interface MainContentProps {
  activeTab: string;
  showOrderForm: boolean;
  currentView: string;
  selectedOrder: Order | null;
  selectedDriver: Driver | null;
  selectedVehicle: Vehicle | null;
  selectedInvoice: Invoice | null;
  showDriverForm: boolean;
  showVehicleForm: boolean;
  showInvoiceForm: boolean;
  showAssignDriverModal: boolean;
  showDeliveryReportForm: boolean;
  selectedDeliveryReport: DeliveryReport | null;
  deliveryReportStage: 'loading' | 'unloading' | 'document-delivery';
  user: { name: string; role: 'admin' | 'financial' | 'operational' | 'driver'; driverType?: 'internal' | 'vendor' };
  orders: Order[];
  drivers: Driver[];
  vehicles: Vehicle[];
  invoices: Invoice[];
  deliveryReports: DeliveryReport[];
  reportStats: ReportStats;
  orderReportData: OrderReportData[];
  driverReportData: DriverReportData[];
  vehicleReportData: VehicleReportData[];
  onCreateOrder: (orderData: any) => void;
  onCancelOrderForm: () => void;
  onShowOrderForm: () => void;
  onViewOrder: (order: Order) => void;
  onEditOrder: (order: Order) => void;
  onAssignDriver: (order: Order) => void;
  onBackToList: () => void;
  onCloseAssignDriverModal: () => void;
  onAssignDriverComplete: (driverData: any) => void;
  onUpdateOrder: (orderData: any) => void;
  // Driver management props
  onCreateDriver: (driverData: any) => void;
  onViewDriver: (driver: Driver) => void;
  onEditDriver: (driver: Driver) => void;
  onToggleDriverStatus: (driverId: string) => void;
  onShowDriverForm: () => void;
  onCancelDriverForm: () => void;
  onUpdateDriver: (driverData: any) => void;
  onBackToDriverList: () => void;
  // Vehicle management props
  onCreateVehicle: (vehicleData: any) => void;
  onViewVehicle: (vehicle: Vehicle) => void;
  onEditVehicle: (vehicle: Vehicle) => void;
  onToggleVehicleStatus: (vehicleId: string) => void;
  onShowVehicleForm: () => void;
  onCancelVehicleForm: () => void;
  onUpdateVehicle: (vehicleData: any) => void;
  onBackToVehicleList: () => void;
  // Invoice management props
  onCreateInvoice: (invoiceData: any) => void;
  onViewInvoice: (invoice: Invoice) => void;
  onEditInvoice: (invoice: Invoice) => void;
  onUpdateInvoiceStatus: (invoiceId: string, status: Invoice['status']) => void;
  onShowInvoiceForm: () => void;
  onCancelInvoiceForm: () => void;
  onUpdateInvoice: (invoiceData: any) => void;
  onBackToInvoiceList: () => void;
  // Delivery report props
  onCreateDeliveryReport: (orderId: string) => void;
  onContinueDeliveryReport: (report: DeliveryReport, stage: 'unloading' | 'document-delivery') => void;
  onSubmitDeliveryReport: (data: DeliveryReportFormData) => void;
  onCancelDeliveryReportForm: () => void;
  onBackToDeliveryReportList: () => void;
}

const MainContent = ({
  activeTab,
  showOrderForm,
  currentView,
  selectedOrder,
  selectedDriver,
  selectedVehicle,
  selectedInvoice,
  showDriverForm,
  showVehicleForm,
  showInvoiceForm,
  showAssignDriverModal,
  showDeliveryReportForm,
  selectedDeliveryReport,
  deliveryReportStage,
  user,
  orders,
  drivers,
  vehicles,
  invoices,
  deliveryReports,
  reportStats,
  orderReportData,
  driverReportData,
  vehicleReportData,
  onCreateOrder,
  onCancelOrderForm,
  onShowOrderForm,
  onViewOrder,
  onEditOrder,
  onAssignDriver,
  onBackToList,
  onCloseAssignDriverModal,
  onAssignDriverComplete,
  onUpdateOrder,
  onCreateDriver,
  onViewDriver,
  onEditDriver,
  onToggleDriverStatus,
  onShowDriverForm,
  onCancelDriverForm,
  onUpdateDriver,
  onBackToDriverList,
  onCreateVehicle,
  onViewVehicle,
  onEditVehicle,
  onToggleVehicleStatus,
  onShowVehicleForm,
  onCancelVehicleForm,
  onUpdateVehicle,
  onBackToVehicleList,
  onCreateInvoice,
  onViewInvoice,
  onEditInvoice,
  onUpdateInvoiceStatus,
  onShowInvoiceForm,
  onCancelInvoiceForm,
  onUpdateInvoice,
  onBackToInvoiceList,
  onCreateDeliveryReport,
  onContinueDeliveryReport,
  onSubmitDeliveryReport,
  onCancelDeliveryReportForm,
  onBackToDeliveryReportList
}: MainContentProps) => {

  switch (activeTab) {
    case 'dashboard':
      return <DashboardSection user={user} />;

    case 'orders':
    case 'my-orders':
      return (
        <OrderSection
          activeTab={activeTab}
          showOrderForm={showOrderForm}
          currentView={currentView}
          selectedOrder={selectedOrder}
          showAssignDriverModal={showAssignDriverModal}
          user={user}
          orders={orders}
          onCreateOrder={onCreateOrder}
          onCancelOrderForm={onCancelOrderForm}
          onShowOrderForm={onShowOrderForm}
          onViewOrder={onViewOrder}
          onEditOrder={onEditOrder}
          onAssignDriver={onAssignDriver}
          onBackToList={onBackToList}
          onCloseAssignDriverModal={onCloseAssignDriverModal}
          onAssignDriverComplete={onAssignDriverComplete}
          onUpdateOrder={onUpdateOrder}
        />
      );

    case 'drivers':
      return (
        <DriverSection
          showDriverForm={showDriverForm}
          currentView={currentView}
          selectedDriver={selectedDriver}
          user={user}
          drivers={drivers}
          onCreateDriver={onCreateDriver}
          onViewDriver={onViewDriver}
          onEditDriver={onEditDriver}
          onToggleDriverStatus={onToggleDriverStatus}
          onShowDriverForm={onShowDriverForm}
          onCancelDriverForm={onCancelDriverForm}
          onUpdateDriver={onUpdateDriver}
          onBackToDriverList={onBackToDriverList}
        />
      );

    case 'vehicles':
      return (
        <VehicleSection
          showVehicleForm={showVehicleForm}
          currentView={currentView}
          selectedVehicle={selectedVehicle}
          user={user}
          vehicles={vehicles}
          onCreateVehicle={onCreateVehicle}
          onViewVehicle={onViewVehicle}
          onEditVehicle={onEditVehicle}
          onToggleVehicleStatus={onToggleVehicleStatus}
          onShowVehicleForm={onShowVehicleForm}
          onCancelVehicleForm={onCancelVehicleForm}
          onUpdateVehicle={onUpdateVehicle}
          onBackToVehicleList={onBackToVehicleList}
        />
      );

    case 'invoices':
      return (
        <InvoiceSection
          showInvoiceForm={showInvoiceForm}
          currentView={currentView}
          selectedInvoice={selectedInvoice}
          user={user}
          invoices={invoices}
          onCreateInvoice={onCreateInvoice}
          onViewInvoice={onViewInvoice}
          onEditInvoice={onEditInvoice}
          onUpdateInvoiceStatus={onUpdateInvoiceStatus}
          onShowInvoiceForm={onShowInvoiceForm}
          onCancelInvoiceForm={onCancelInvoiceForm}
          onUpdateInvoice={onUpdateInvoice}
          onBackToInvoiceList={onBackToInvoiceList}
        />
      );

    case 'delivery-report':
      return (
        <DeliveryReportSection
          showDeliveryReportForm={showDeliveryReportForm}
          selectedDeliveryReport={selectedDeliveryReport}
          deliveryReportStage={deliveryReportStage}
          user={user}
          deliveryReports={deliveryReports}
          orders={orders}
          onCreateDeliveryReport={onCreateDeliveryReport}
          onContinueDeliveryReport={onContinueDeliveryReport}
          onSubmitDeliveryReport={onSubmitDeliveryReport}
          onCancelDeliveryReportForm={onCancelDeliveryReportForm}
          onBackToDeliveryReportList={onBackToDeliveryReportList}
        />
      );

    case 'reports':
      return (
        <ReportSection
          reportStats={reportStats}
          orderReportData={orderReportData}
          driverReportData={driverReportData}
          vehicleReportData={vehicleReportData}
        />
      );
    
    default:
      return (
        <div className="text-center py-8 max-w-full">
          <h2 className="text-xl sm:text-2xl font-bold text-gray-900">
            Fitur {activeTab} sedang dalam pengembangan
          </h2>
          <p className="text-sm sm:text-base text-gray-600 mt-2">
            Fitur ini akan segera tersedia
          </p>
        </div>
      );
  }
};

export default MainContent;
