import React, { useState } from 'react';
import LoginForm from '@/components/Auth/LoginForm';
import Navbar from '@/components/Layout/Navbar';
import Sidebar from '@/components/Layout/Sidebar';
import MainContent from '@/components/Layout/MainContent';
import MobileHoverZone from '@/components/Layout/MobileHoverZone';
import { useAuth } from '@/hooks/useAuth';
import { useOrderManagement } from '@/hooks/useOrderManagement';
import { useDriverManagement } from '@/hooks/useDriverManagement';
import { useVehicleManagement } from '@/hooks/useVehicleManagement';
import { useInvoiceManagement } from '@/hooks/useInvoiceManagement';
import { useDeliveryReportManagement } from '@/hooks/useDeliveryReportManagement';
import { useReportData } from '@/hooks/useReportData';
import { useMobileSidebar } from '@/hooks/useMobileSidebar';
import { useIsMobile } from '@/hooks/use-mobile';
import { Order } from '@/types/order';
import { Driver } from '@/types/driver';
import { Vehicle } from '@/types/vehicle';
import { Invoice } from '@/types/invoice';
import { DeliveryReport, DeliveryReportFormData } from '@/types/deliveryReport';

const Index = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showOrderForm, setShowOrderForm] = useState(false);
  const [showDriverForm, setShowDriverForm] = useState(false);
  const [showVehicleForm, setShowVehicleForm] = useState(false);
  const [showInvoiceForm, setShowInvoiceForm] = useState(false);
  const [showDeliveryReportForm, setShowDeliveryReportForm] = useState(false);
  const [currentView, setCurrentView] = useState('list');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [selectedDeliveryReport, setSelectedDeliveryReport] = useState<DeliveryReport | null>(null);
  const [deliveryReportStage, setDeliveryReportStage] = useState<'loading' | 'unloading' | 'document-delivery'>('loading');
  const [showAssignDriverModal, setShowAssignDriverModal] = useState(false);
  
  const { user, isLoading, handleLogin, handleLogout } = useAuth();
  const { 
    orders, 
    handleCreateOrder, 
    handleViewOrder, 
    handleEditOrder, 
    handleAssignDriver 
  } = useOrderManagement();
  
  const {
    drivers,
    handleCreateDriver,
    handleUpdateDriver,
    handleToggleDriverStatus,
    handleDeleteDriver
  } = useDriverManagement();

  const {
    vehicles,
    handleCreateVehicle,
    handleUpdateVehicle,
    handleToggleVehicleStatus,
    handleDeleteVehicle
  } = useVehicleManagement();

  const {
    invoices,
    handleCreateInvoice,
    handleUpdateInvoice,
    handleUpdateInvoiceStatus,
    handleDeleteInvoice
  } = useInvoiceManagement();

  const {
    deliveryReports,
    handleCreateDeliveryReport,
    handleUpdateDeliveryReport,
    getReportByOrderId
  } = useDeliveryReportManagement();

  const {
    stats: reportStats,
    orderReportData,
    driverReportData,
    vehicleReportData
  } = useReportData(orders, drivers, vehicles);
  
  const { isVisible, hoverZoneProps, sidebarProps } = useMobileSidebar();
  const isMobile = useIsMobile();

  const handleCreateOrderAndClose = (orderData: any) => {
    handleCreateOrder(orderData);
    setShowOrderForm(false);
  };

  const handleViewOrderAndNavigate = (order: Order) => {
    setSelectedOrder(order);
    setCurrentView('order-detail');
    handleViewOrder(order, () => {
      setSelectedOrder(order);
      setCurrentView('order-detail');
    });
  };

  const handleEditOrderAndNavigate = (order: Order) => {
    setSelectedOrder(order);
    setCurrentView('order-edit');
    handleEditOrder(order, null, () => {
      setSelectedOrder(order);
      setCurrentView('order-edit');
    });
  };

  const handleAssignDriverAndModal = (order: Order) => {
    setSelectedOrder(order);
    setShowAssignDriverModal(true);
    handleAssignDriver(order, null, () => {
      setSelectedOrder(order);
      setShowAssignDriverModal(true);
    });
  };

  const handleBackToList = () => {
    setCurrentView('list');
    setSelectedOrder(null);
  };

  const handleCloseAssignDriverModal = () => {
    setShowAssignDriverModal(false);
    setSelectedOrder(null);
  };

  const handleAssignDriverComplete = (driverData: any) => {
    if (selectedOrder) {
      handleAssignDriver(selectedOrder, driverData);
    }
    handleCloseAssignDriverModal();
  };

  const handleUpdateOrder = (orderData: any) => {
    if (selectedOrder) {
      handleEditOrder(selectedOrder, orderData);
    }
    setCurrentView('list');
    setSelectedOrder(null);
  };

  // Driver management handlers
  const handleCreateDriverAndClose = (driverData: any) => {
    handleCreateDriver(driverData);
    setShowDriverForm(false);
  };

  const handleViewDriverAndNavigate = (driver: Driver) => {
    setSelectedDriver(driver);
    setCurrentView('driver-detail');
  };

  const handleEditDriverAndNavigate = (driver: Driver) => {
    setSelectedDriver(driver);
    setCurrentView('driver-edit');
    setShowDriverForm(true);
  };

  const handleUpdateDriverAndClose = (driverData: any) => {
    if (selectedDriver) {
      handleUpdateDriver(selectedDriver.id, driverData);
    }
    setShowDriverForm(false);
    setCurrentView('list');
    setSelectedDriver(null);
  };

  const handleBackToDriverList = () => {
    setCurrentView('list');
    setSelectedDriver(null);
    setShowDriverForm(false);
  };

  // Vehicle management handlers
  const handleCreateVehicleAndClose = (vehicleData: any) => {
    handleCreateVehicle(vehicleData);
    setShowVehicleForm(false);
  };

  const handleViewVehicleAndNavigate = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
    setCurrentView('vehicle-detail');
  };

  const handleEditVehicleAndNavigate = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
    setCurrentView('vehicle-edit');
    setShowVehicleForm(true);
  };

  const handleUpdateVehicleAndClose = (vehicleData: any) => {
    if (selectedVehicle) {
      handleUpdateVehicle(selectedVehicle.id, vehicleData);
    }
    setShowVehicleForm(false);
    setCurrentView('list');
    setSelectedVehicle(null);
  };

  const handleBackToVehicleList = () => {
    setCurrentView('list');
    setSelectedVehicle(null);
    setShowVehicleForm(false);
  };

  // Invoice management handlers
  const handleCreateInvoiceAndClose = (invoiceData: any) => {
    handleCreateInvoice(invoiceData);
    setShowInvoiceForm(false);
  };

  const handleViewInvoiceAndNavigate = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setCurrentView('invoice-detail');
  };

  const handleEditInvoiceAndNavigate = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setCurrentView('invoice-edit');
    setShowInvoiceForm(true);
  };

  const handleUpdateInvoiceAndClose = (invoiceData: any) => {
    if (selectedInvoice) {
      handleUpdateInvoice(selectedInvoice.id, invoiceData);
    }
    setShowInvoiceForm(false);
    setCurrentView('list');
    setSelectedInvoice(null);
  };

  const handleBackToInvoiceList = () => {
    setCurrentView('list');
    setSelectedInvoice(null);
    setShowInvoiceForm(false);
  };

  // Delivery report management handlers
  const handleCreateDeliveryReportAndNavigate = (orderId: string) => {
    const mockReport: DeliveryReport = {
      id: 'temp-id',
      orderId,
      orderNumber: orderId,
      driverType: 'internal', // Default to internal
      driverId: 'current-driver',
      driverName: user?.name || 'Current Driver',
      status: 'draft',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    setSelectedDeliveryReport(mockReport);
    setDeliveryReportStage('loading');
    setShowDeliveryReportForm(true);
  };

  const handleContinueDeliveryReportAndNavigate = (report: DeliveryReport, stage: 'unloading' | 'document-delivery') => {
    setSelectedDeliveryReport(report);
    setDeliveryReportStage(stage);
    setShowDeliveryReportForm(true);
  };

  const handleSubmitDeliveryReportAndClose = (data: DeliveryReportFormData) => {
    if (selectedDeliveryReport?.id === 'temp-id') {
      // Creating new report
      handleCreateDeliveryReport(data);
    } else if (selectedDeliveryReport) {
      // Updating existing report
      handleUpdateDeliveryReport(selectedDeliveryReport.id, data, deliveryReportStage as 'unloading' | 'document-delivery');
    }
    
    setShowDeliveryReportForm(false);
    setSelectedDeliveryReport(null);
  };

  const handleCancelDeliveryReportForm = () => {
    setShowDeliveryReportForm(false);
    setSelectedDeliveryReport(null);
  };

  const handleBackToDeliveryReportList = () => {
    setShowDeliveryReportForm(false);
    setSelectedDeliveryReport(null);
  };

  const handleLogoutAndReset = () => {
    handleLogout();
    setActiveTab('dashboard');
    setCurrentView('list');
    setSelectedOrder(null);
    setSelectedDriver(null);
    setSelectedVehicle(null);
    setSelectedInvoice(null);
    setSelectedDeliveryReport(null);
    setShowOrderForm(false);
    setShowDriverForm(false);
    setShowVehicleForm(false);
    setShowInvoiceForm(false);
    setShowDeliveryReportForm(false);
    setShowAssignDriverModal(false);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    
    // Reset view state when changing tabs
    setCurrentView('list');
    
    // Special handling for driver report tabs
    if (tab === 'loading-report') {
      // For loading report, we need to show available orders
      setCurrentView('list');
      // Get available orders for the driver
      const availableOrders = orders.filter(order => 
        order.status === 'assigned' && 
        order.driverId === user.id &&
        !deliveryReports.some(r => r.orderId === order.id)
      );
      
      if (availableOrders.length > 0) {
        setSelectedOrder(availableOrders[0]);
      }
    } else if (tab === 'unloading-report' && user?.driverType === 'vendor') {
      // For unloading report, we need to show reports that are in loading-reported status
      const pendingReports = deliveryReports.filter(report => 
        report.driverId === user.id && 
        report.status === 'loading-reported'
      );
      
      if (pendingReports.length > 0) {
        setSelectedDeliveryReport(pendingReports[0]);
      }
    }
  };

  const handleLoadingReportClick = () => {
    setActiveTab('loading-report');
    setCurrentView('loading-report');
    
    // Get the first available order for the driver
    const availableOrder = orders.find(order => 
      order.status === 'assigned' && 
      order.driverId === user.id &&
      !deliveryReports.some(r => r.orderId === order.id)
    );
    
    if (availableOrder) {
      setSelectedOrder(availableOrder);
    }
  };

  const handleDriverReportTabClick = (tab: string) => {
    setActiveTab(tab);
    
    if (tab === 'loading-report') {
      // Find an available order for the driver
      const availableOrder = orders.find(order => 
        order.status === 'assigned' && 
        order.driverId === user.id &&
        !deliveryReports.some(r => r.orderId === order.id)
      );
      
      if (availableOrder) {
        setSelectedOrder(availableOrder);
        setCurrentView('loading-report');
      } else {
        // No available orders, show a message
        toast({
          title: "Tidak ada pesanan tersedia",
          description: "Tidak ada pesanan yang dapat dilaporkan saat ini.",
          variant: "destructive"
        });
      }
    } else if (tab === 'unloading-report' && user?.driverType === 'vendor') {
      // Find a report that needs unloading
      const pendingReport = deliveryReports.find(report => 
        report.driverId === user.id && 
        report.status === 'loading-reported'
      );
      
      if (pendingReport) {
        setSelectedDeliveryReport(pendingReport);
        setCurrentView('unloading-report');
      } else {
        // No pending reports, show a message
        toast({
          title: "Tidak ada laporan bongkar",
          description: "Tidak ada pesanan yang perlu dilaporkan bongkar saat ini.",
          variant: "destructive"
        });
      }
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <LoginForm onLogin={handleLogin} isLoading={isLoading} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col w-full">
      <Navbar user={user} onLogout={handleLogoutAndReset} />
      
      <div className="flex flex-1 overflow-hidden relative">
        {/* Mobile hover zone */}
        <MobileHoverZone {...hoverZoneProps} isVisible={isVisible} />
        
        {/* Sidebar */}
        <Sidebar
          userRole={user.role}
          driverType={user.driverType}
          activeTab={activeTab}
          onTabChange={(tab) => {
            if (tab === 'loading-report' || tab === 'unloading-report') {
              handleDriverReportTabClick(tab);
            } else {
              setActiveTab(tab);
              setCurrentView('list');
            }
          }}
          isVisible={isVisible}
          onMouseLeave={hoverZoneProps.onMouseLeave}
        />
        
        {/* Main content */}
        <main className={`flex-1 overflow-auto transition-all duration-300 ${
          isMobile ? 'w-full' : (isVisible ? 'ml-0' : 'ml-0')
        }`}>
          <div className="p-4 sm:p-6">
            <MainContent
              activeTab={activeTab}
              showOrderForm={showOrderForm}
              showDriverForm={showDriverForm}
              showVehicleForm={showVehicleForm}
              showInvoiceForm={showInvoiceForm}
              showDeliveryReportForm={showDeliveryReportForm}
              currentView={currentView}
              selectedOrder={selectedOrder}
              selectedDriver={selectedDriver}
              selectedVehicle={selectedVehicle}
              selectedInvoice={selectedInvoice}
              selectedDeliveryReport={selectedDeliveryReport}
              deliveryReportStage={deliveryReportStage}
              showAssignDriverModal={showAssignDriverModal}
              user={{...user, driverType: 'internal'}} // Default to internal for compatibility
              orders={orders}
              drivers={drivers}
              vehicles={vehicles}
              invoices={invoices}
              deliveryReports={deliveryReports}
              reportStats={reportStats}
              orderReportData={orderReportData}
              driverReportData={driverReportData}
              vehicleReportData={vehicleReportData}
              onCreateOrder={handleCreateOrderAndClose}
              onCancelOrderForm={() => setShowOrderForm(false)}
              onShowOrderForm={() => setShowOrderForm(true)}
              onViewOrder={handleViewOrderAndNavigate}
              onEditOrder={handleEditOrderAndNavigate}
              onAssignDriver={handleAssignDriverAndModal}
              onBackToList={handleBackToList}
              onCloseAssignDriverModal={handleCloseAssignDriverModal}
              onAssignDriverComplete={handleAssignDriverComplete}
              onUpdateOrder={handleUpdateOrder}
              onCreateDriver={handleCreateDriverAndClose}
              onViewDriver={handleViewDriverAndNavigate}
              onEditDriver={handleEditDriverAndNavigate}
              onToggleDriverStatus={handleToggleDriverStatus}
              onShowDriverForm={() => setShowDriverForm(true)}
              onCancelDriverForm={() => setShowDriverForm(false)}
              onUpdateDriver={handleUpdateDriverAndClose}
              onBackToDriverList={handleBackToDriverList}
              onCreateVehicle={handleCreateVehicleAndClose}
              onViewVehicle={handleViewVehicleAndNavigate}
              onEditVehicle={handleEditVehicleAndNavigate}
              onToggleVehicleStatus={handleToggleVehicleStatus}
              onShowVehicleForm={() => setShowVehicleForm(true)}
              onCancelVehicleForm={() => setShowVehicleForm(false)}
              onUpdateVehicle={handleUpdateVehicleAndClose}
              onBackToVehicleList={handleBackToVehicleList}
              onCreateInvoice={handleCreateInvoiceAndClose}
              onViewInvoice={handleViewInvoiceAndNavigate}
              onEditInvoice={handleEditInvoiceAndNavigate}
              onUpdateInvoiceStatus={handleUpdateInvoiceStatus}
              onShowInvoiceForm={() => setShowInvoiceForm(true)}
              onCancelInvoiceForm={() => setShowInvoiceForm(false)}
              onUpdateInvoice={handleUpdateInvoiceAndClose}
              onBackToInvoiceList={handleBackToInvoiceList}
              onCreateDeliveryReport={handleCreateDeliveryReportAndNavigate}
              onContinueDeliveryReport={handleContinueDeliveryReportAndNavigate}
              onSubmitDeliveryReport={handleSubmitDeliveryReportAndClose}
              onCancelDeliveryReportForm={handleCancelDeliveryReportForm}
              onBackToDeliveryReportList={handleBackToDeliveryReportList}
            />
          </div>
        </main>
      </div>
    </div>
  );
};

export default Index;
